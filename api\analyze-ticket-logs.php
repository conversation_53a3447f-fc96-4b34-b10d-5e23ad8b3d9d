<?php
// Analyze ticket API logs to identify issues

$logFile = '../logs/ticket_api.log';

if (!file_exists($logFile)) {
    echo "<h1>Log Analysis</h1>";
    echo "<p style='color: red;'>❌ Log file not found: {$logFile}</p>";
    exit;
}

echo "<h1>Ticket API Log Analysis</h1>";

// Read log file
$logContent = file_get_contents($logFile);
$lines = explode("\n", $logContent);

echo "<h2>📊 Log Statistics</h2>";
echo "<ul>";
echo "<li><strong>Total Lines:</strong> " . count($lines) . "</li>";
echo "<li><strong>File Size:</strong> " . formatBytes(filesize($logFile)) . "</li>";
echo "<li><strong>Last Modified:</strong> " . date('Y-m-d H:i:s', filemtime($logFile)) . "</li>";
echo "</ul>";

// Analyze each log entry
$successCount = 0;
$errorCount = 0;
$authErrors = 0;
$serverErrors = 0;
$validationErrors = 0;
$otherErrors = 0;

$recentEntries = [];
$errorSummary = [];

foreach ($lines as $lineNum => $line) {
    if (empty(trim($line))) continue;
    
    // Parse log entry
    if (preg_match('/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - Ticket ID: (\d+) - GraphQL API Result: (.+)$/', $line, $matches)) {
        $timestamp = $matches[1];
        $ticketId = $matches[2];
        $jsonResult = $matches[3];
        
        $result = json_decode($jsonResult, true);
        
        if ($result) {
            $entry = [
                'line' => $lineNum + 1,
                'timestamp' => $timestamp,
                'ticket_id' => $ticketId,
                'result' => $result
            ];
            
            if ($result['success'] === true) {
                $successCount++;
                $entry['status'] = 'SUCCESS';
            } else {
                $errorCount++;
                $entry['status'] = 'ERROR';
                
                // Categorize errors
                $status = $result['status'] ?? 0;
                if ($status == 401) {
                    $authErrors++;
                    $entry['error_type'] = 'AUTHENTICATION';
                } elseif ($status >= 500) {
                    $serverErrors++;
                    $entry['error_type'] = 'SERVER_ERROR';
                } elseif (isset($result['data']['errors'])) {
                    $validationErrors++;
                    $entry['error_type'] = 'VALIDATION';
                    
                    // Extract error messages
                    $errors = $result['data']['errors'];
                    $errorMessages = [];
                    foreach ($errors as $error) {
                        $errorMessages[] = $error['message'] ?? 'Unknown error';
                    }
                    $entry['error_messages'] = $errorMessages;
                } else {
                    $otherErrors++;
                    $entry['error_type'] = 'OTHER';
                }
            }
            
            $recentEntries[] = $entry;
        }
    }
}

// Keep only recent entries (last 20)
$recentEntries = array_slice($recentEntries, -20);

echo "<h2>📈 Error Summary</h2>";
echo "<div style='display: flex; gap: 20px; flex-wrap: wrap;'>";

echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 200px;'>";
echo "<h3 style='color: green; margin-top: 0;'>✅ Successful</h3>";
echo "<p style='font-size: 24px; margin: 0;'>{$successCount}</p>";
echo "</div>";

echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 200px;'>";
echo "<h3 style='color: red; margin-top: 0;'>❌ Failed</h3>";
echo "<p style='font-size: 24px; margin: 0;'>{$errorCount}</p>";
echo "</div>";

echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 200px;'>";
echo "<h3 style='color: orange; margin-top: 0;'>🔐 Auth Errors (401)</h3>";
echo "<p style='font-size: 24px; margin: 0;'>{$authErrors}</p>";
echo "</div>";

echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 200px;'>";
echo "<h3 style='color: purple; margin-top: 0;'>⚠️ Validation Errors</h3>";
echo "<p style='font-size: 24px; margin: 0;'>{$validationErrors}</p>";
echo "</div>";

echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px; min-width: 200px;'>";
echo "<h3 style='color: darkred; margin-top: 0;'>🔥 Server Errors (5xx)</h3>";
echo "<p style='font-size: 24px; margin: 0;'>{$serverErrors}</p>";
echo "</div>";

echo "</div>";

echo "<h2>📋 Recent Log Entries (Last 20)</h2>";
echo "<div style='max-height: 600px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;'>";

foreach (array_reverse($recentEntries) as $entry) {
    $statusColor = $entry['status'] == 'SUCCESS' ? 'green' : 'red';
    $errorType = $entry['error_type'] ?? '';
    
    echo "<div style='border-bottom: 1px solid #eee; padding: 10px 0;'>";
    echo "<p><strong>Line {$entry['line']}:</strong> {$entry['timestamp']} - Ticket ID: {$entry['ticket_id']}</p>";
    echo "<p><span style='color: {$statusColor}; font-weight: bold;'>{$entry['status']}</span>";
    
    if ($errorType) {
        echo " - <span style='color: orange;'>{$errorType}</span>";
    }
    
    echo "</p>";
    
    if (isset($entry['error_messages'])) {
        echo "<ul>";
        foreach ($entry['error_messages'] as $msg) {
            echo "<li style='color: red;'>{$msg}</li>";
        }
        echo "</ul>";
    }
    
    // Show HTTP status
    if (isset($entry['result']['status'])) {
        echo "<p><small>HTTP Status: {$entry['result']['status']}</small></p>";
    }
    
    echo "</div>";
}

echo "</div>";

// Recommendations
echo "<h2>💡 Recommendations</h2>";

if ($authErrors > 0) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>🔐 Authentication Issues</h3>";
    echo "<p>You have {$authErrors} authentication errors (401). This means:</p>";
    echo "<ul>";
    echo "<li>API key is invalid or expired</li>";
    echo "<li>Check if you're using the correct API key</li>";
    echo "<li>Verify the API endpoint is correct</li>";
    echo "</ul>";
    echo "<p><strong>Action:</strong> Update API key in your create-ticket files.</p>";
    echo "</div>";
}

if ($validationErrors > 0) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>⚠️ Validation Issues</h3>";
    echo "<p>You have {$validationErrors} validation errors. This means:</p>";
    echo "<ul>";
    echo "<li>Required fields are missing</li>";
    echo "<li>Field values are in wrong format</li>";
    echo "<li>GraphQL mutation syntax errors</li>";
    echo "</ul>";
    echo "<p><strong>Action:</strong> Check the error messages above for specific field issues.</p>";
    echo "</div>";
}

if ($serverErrors > 0) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>🔥 Server Issues</h3>";
    echo "<p>You have {$serverErrors} server errors (5xx). This means:</p>";
    echo "<ul>";
    echo "<li>Appika API server is having issues</li>";
    echo "<li>Internal server errors on their side</li>";
    echo "<li>Temporary service unavailability</li>";
    echo "</ul>";
    echo "<p><strong>Action:</strong> Wait and retry, or contact Appika support.</p>";
    echo "</div>";
}

if ($successCount > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>✅ Good News!</h3>";
    echo "<p>You have {$successCount} successful API calls, which means your integration is working when conditions are right.</p>";
    echo "</div>";
}

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
