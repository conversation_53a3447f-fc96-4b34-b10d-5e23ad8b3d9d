# API Connection Examples

This directory contains examples of how to connect to the API endpoint:

```
https://dev-api-pooh-sgsg.appika.com/contacts/customer
```

## Files

1. `core_api_example.php` - Example using Core API (REST) approach
2. `graphql_example.php` - Example using GraphQL approach

## API Key

Both examples use the following API key:

```
NjhhNTNhMWVkZDc0Zjk0MGRkMmFiZmRjNDEwMDQ5ZDc0Yjg2NTU4NzJkMjA5NTFkMzc5OWQ5MzVkNzg4ODY5Yw
```

## How to Use

### Core API Example

1. Open `core_api_example.php` in your browser or run it via command line:

   ```
   php api/core_api_example.php
   ```

2. The example demonstrates two approaches:

   - Basic GET request to the endpoint
   - GET request with query parameters

3. The results will be displayed on the page, including:
   - HTTP status code
   - Response data (formatted as JSON if applicable)
   - Any errors that occur

### GraphQL Example

1. Open `graphql_example.php` in your browser or run it via command line:

   ```
   php api/graphql_example.php
   ```

2. The example demonstrates:

   - Basic GraphQL query
   - Alternative query structure
   - Instructions for using a dedicated GraphQL client

3. The results will be displayed on the page, including:
   - HTTP status code
   - Response data (formatted as JSON)
   - Any errors that occur

## Troubleshooting

If you encounter a "ResourceNotFoundException" error, it could mean:

1. The endpoint doesn't exist or isn't properly configured on the server
2. The API requires additional parameters
3. The authentication token is invalid or expired
4. The API doesn't support the method you're using (GET vs POST)

### Solutions:

1. Check if the API endpoint is correct
2. Try adding query parameters
3. Verify your API key is valid
4. Contact the API provider for assistance

## Installing a Dedicated GraphQL Client

For a better GraphQL experience, you can install a dedicated GraphQL client:

```
composer require gmostafa/php-graphql-client
```

The `graphql_example.php` file includes example code for using this client.

## Notes

- The GraphQL queries in the examples are samples and may need to be adjusted based on the actual schema provided by the API.
- The GraphQL endpoint is assumed to be `/graphql` - this may need to be adjusted based on the actual API configuration.
- Both examples include error handling to help diagnose issues.
