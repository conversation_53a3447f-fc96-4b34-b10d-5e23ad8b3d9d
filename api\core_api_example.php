<?php
/**
 * Core API Example - Using Guzzle to connect to the API
 *
 * This example demonstrates how to connect to the API endpoint:
 * https://api-sgsg.appika.com/contacts/customer
 * using the Core API (REST) approach with Guzzle HTTP client.
 */

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load centralized API configuration
require_once __DIR__ . '/../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $apiEndpoint,
    'timeout' => 30,
    'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
]);

// Function to display results in a readable format
function displayResults($title, $data) {
    echo "<h2>{$title}</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Make the API request
try {
    echo "<h1>Core API Example</h1>";

    // Send the GET request
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
        ],
    ]);

    // Get status code
    $statusCode = $response->getStatusCode();
    echo "<p>Status Code: {$statusCode}</p>";

    // Get response body
    $body = $response->getBody()->getContents();

    // Parse JSON if the response is JSON
    if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
        $data = json_decode($body, true);
        displayResults('Response Data (JSON):', $data);
    } else {
        displayResults('Response Body:', $body);
    }

} catch (\Exception $e) {
    echo "<h2>Error Occurred</h2>";
    echo "<p>Error Message: " . $e->getMessage() . "</p>";
}

// Alternative approach with query parameters
echo "<h1>Alternative Approach (with query parameters)</h1>";

try {
    // Send the GET request with query parameters
    $response = $client->request('GET', $apiPath, [
        'headers' => [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
        ],
        'query' => [
            'limit' => 10,
            'offset' => 0,
            // Add any other query parameters that might be required
        ],
    ]);

    // Get status code
    $statusCode = $response->getStatusCode();
    echo "<p>Status Code: {$statusCode}</p>";

    // Get response body
    $body = $response->getBody()->getContents();

    // Parse JSON if the response is JSON
    if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
        $data = json_decode($body, true);
        displayResults('Response Data with Query Parameters (JSON):', $data);
    } else {
        displayResults('Response Body with Query Parameters:', $body);
    }

} catch (\Exception $e) {
    echo "<h2>Error Occurred</h2>";
    echo "<p>Error Message: " . $e->getMessage() . "</p>";
}
?>