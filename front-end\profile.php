<?php
include('../includes/noindex-header.php');
session_start();
include('../functions/server.php');
include('../functions/maintenance-check.php');
include('../functions/customer-data-service.php');

// Check if user panel is accessible (strict mode)
checkMaintenanceStatus('strict');

// Block suspended/deleted users
if (isset($_SESSION['username'])) {
    $user = mysqli_fetch_assoc(mysqli_query($conn, "SELECT status FROM user WHERE username = '" . $_SESSION['username'] . "'"));
    if ($user && ($user['status'] === 'i' || $user['status'] === 'd')) {
        $_SESSION['error'] = $user['status'] === 'i' ? 'Your account is suspended. Please contact support.' : 'Your account has been deleted. Please contact support.';
        session_destroy();
        unset($_SESSION['username']);
        header('Location: sign-in.php');
        exit();
    }
}

if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Home</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="../css/wpcss/select2.css" rel="stylesheet" />

    <!-- Additional Select2 Modal Styling -->
    <style>
    /* Ensure Select2 works properly in modals */
    .select2-container {
        z-index: 9999 !important;
        width: 100% !important;
    }

    .select2-dropdown {
        z-index: 9999 !important;
    }

    /* Style Select2 to match Bootstrap form controls */
    .select2-container--default .select2-selection--single {
        height: 38px !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        padding: 6px 12px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 24px !important;
        padding-left: 0 !important;
        color: #495057 !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px !important;
        right: 10px !important;
    }

    /* Focus state */
    .select2-container--default.select2-container--focus .select2-selection--single {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    /* Dropdown styling */
    .select2-container--default .select2-results__option {
        padding: 8px 12px !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007bff !important;
    }
    </style>
    <style>
    p {
        font-size: 16px !important;
    }

    .bg-default-2.pb-17.pb-md-29 {
        margin-top: -65px;
    }

    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    body {
        margin-top: -48px !important;

    }

    @media (max-width: 767px) {
        body {
            margin-top: -90px !important;
        }
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    /* Container width to match my-ticket.php */
    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Custom modal width */
    /* Edit profile field  */
    .custom-width-modal {
        max-width: 1200px;
        /* You can adjust this value as needed */
        width: 90%;
        /* This makes it responsive */
    }

    /* Make sure the modal content has enough space */
    .custom-width-modal .modal-content {
        padding: 20px;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .custom-width-modal {
            max-width: 90%;
        }
    }

    @media (max-width: 576px) {
        .custom-width-modal {
            max-width: 95%;
            margin: 0.5rem auto;
        }

        .custom-width-modal .modal-content {
            padding: 15px;
        }
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        padding: 8px 16px;
        background-color: #473BF0;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .custom-alert-btn:hover {
        background-color: #3b31c8;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(71, 59, 240, 0.3);
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }

    /* Success Animation Styles */
    .success-animation {
        margin: 0 auto;
        width: 100px;
        height: 100px;
    }

    .checkmark {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: block;
        stroke-width: 2;
        stroke: #4bb71b;
        stroke-miterlimit: 10;
        box-shadow: inset 0px 0px 0px #4bb71b;
        animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    }

    .checkmark__circle {
        stroke-dasharray: 166;
        stroke-dashoffset: 166;
        stroke-width: 1;
        stroke-miterlimit: 2;
        stroke: #4bb71b;
        fill: none;
        animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards, resetStrokeWidth 0.1s 1s forwards;
    }

    .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
    }

    @keyframes stroke {
        100% {
            stroke-dashoffset: 0;
        }
    }

    @keyframes scale {

        0%,
        100% {
            transform: none;
        }

        50% {
            transform: scale3d(1.1, 1.1, 1);
        }
    }

    @keyframes fill {
        100% {
            box-shadow: inset 0px 0px 0px 1px #4bb71b;
        }
    }

    @keyframes resetStrokeWidth {
        0% {
            stroke-width: 1;
        }

        100% {
            stroke-width: 1;
        }
    }

    /* Modal fullscreen on mobile */
    @media (max-width: 767px) {
        .bg-default-2.pb-17.pb-md-29 {
            margin-top: 1px;
        }

        .modal-fullscreen-mobile {
            padding: 0 !important;
            margin: 0 !important;
        }

        .modal-fullscreen-mobile .modal-dialog {
            width: 100% !important;
            max-width: none !important;
            height: 100% !important;
            margin: 0 !important;
        }

        .modal-fullscreen-mobile .modal-content {
            height: 100% !important;
            border: 0 !important;
            border-radius: 0 !important;
            box-shadow: none !important;
        }

        .modal-fullscreen-mobile .modal-body {
            overflow-y: auto !important;
            padding: 15px !important;
        }

        .modal-fullscreen-mobile .modal-header,
        .modal-fullscreen-mobile .modal-footer {
            border-radius: 0 !important;
            padding: 15px !important;
        }

        /* Adjust form elements for better mobile experience */
        .modal-fullscreen-mobile .form-control {
            font-size: 16px !important;
            /* Prevents iOS zoom on focus */
            padding: 12px !important;
        }

        .modal-fullscreen-mobile .btn {
            padding: 12px 15px !important;
            font-size: 16px !important;
        }

        .modal-fullscreen-mobile .modal-title {
            font-size: 18px !important;
        }

        /* Ensure the form takes full height */
        .modal-fullscreen-mobile form {
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
        }

        .modal-fullscreen-mobile .modal-body {
            flex: 1 !important;
        }
    }

    /* Enhanced Modal Styling */
    #editProfileModal .modal-content {
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: none;
        overflow: hidden;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    #editProfileModal .modal-header {
        background: linear-gradient(135deg, #473BF0 0%, #6c5ce7 100%);
        color: #fff;
        border: none;
        padding: 25px 30px;
        position: relative;
    }

    #editProfileModal .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        pointer-events: none;
    }

    #editProfileModal .modal-title {
        font-size: 24px !important;
        font-weight: 600 !important;
        color: white !important;
        display: flex;
        align-items: center;
        gap: 12px;
        position: relative;
        z-index: 1;
    }

    #editProfileModal .modal-title::before {
        content: '👤';
        font-size: 28px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    #editProfileModal .modal-body {
        padding: 35px 30px;
        background: #ffffff;
    }

    #editProfileModal .form-label {
        font-size: 15px !important;
        font-weight: 600 !important;
        color: #2c3e50 !important;
        margin-bottom: 8px !important;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    #editProfileModal .form-control {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 15px;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    #editProfileModal .form-control:focus {
        border-color: #473BF0;
        box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.15);
        background: #ffffff;
        transform: translateY(-1px);
    }

    #editProfileModal .form-control:hover {
        border-color: #6c5ce7;
        background: #ffffff;
    }

    #editProfileModal textarea.form-control {
        resize: vertical;
        min-height: 100px;
    }

    #editProfileModal select.form-control {
        background-image: none !important;
        /* Remove custom background image for arrow */
    }

    /* Form Section Styling */
    #editProfileModal .row.mb-3 {
        margin-bottom: 25px !important;
    }

    #editProfileModal .mb-3 {
        margin-bottom: 25px !important;
    }

    /* Form labels without icons */

    /* Enhanced Modal Footer */
    #editProfileModal .modal-footer {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        padding: 25px 30px;
        border-radius: 0 0 20px 20px;
    }

    /* Enhanced Button Styling */
    .modal-btn-primary {
        background: linear-gradient(135deg, #473BF0 0%, #6c5ce7 100%) !important;
        color: white !important;
        border: none !important;
        padding: 12px 24px !important;
        font-size: 15px !important;
        font-weight: 600 !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
        min-width: 140px !important;
        box-shadow: 0 4px 15px rgba(71, 59, 240, 0.3) !important;
    }

    .modal-btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(71, 59, 240, 0.4) !important;
        background: linear-gradient(135deg, #5a4fcf 0%, #7b6ce8 100%) !important;
    }

    .modal-btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #868e96 100%) !important;
        color: white !important;
        border: none !important;
        padding: 12px 24px !important;
        font-size: 15px !important;
        font-weight: 600 !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
        min-width: 140px !important;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
    }

    .modal-btn-cancel:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
        background: linear-gradient(135deg, #5a6268 0%, #7a8288 100%) !important;
    }

    /* Select2 Enhancements */
    .select2-container {
        width: 100% !important;
        max-width: 100% !important;
    }

    .select2-dropdown {
        max-height: 300px !important;
        overflow-y: auto !important;
        border-radius: 12px !important;
        border: 2px solid #e9ecef !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    }

    .select2-container--default .select2-selection--single {
        border: 2px solid #e9ecef !important;
        border-radius: 12px !important;
        height: 48px !important;
        padding: 8px 12px !important;
        background: #f8f9fa !important;
    }

    .select2-container--default .select2-selection--single:focus {
        border-color: #473BF0 !important;
    }

    /* Enhanced Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        height: 48px !important;
        border: 2px solid #e9ecef !important;
        border-radius: 12px !important;
        background-color: #f8f9fa !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 44px !important;
        padding-left: 15px !important;
        padding-right: 30px !important;
        color: #495057 !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 46px !important;
        right: 8px !important;
    }

    .select2-dropdown {
        margin-top: -1px !important;
        border: 2px solid #473BF0 !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 12px rgba(71, 59, 240, 0.15) !important;
        margin-top: 4px !important;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 2px solid #e9ecef !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field:focus {
        border-color: #473BF0 !important;
        outline: none !important;
    }

    .select2-container--default .select2-results__option {
        padding: 10px 15px !important;
        font-size: 15px !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #473BF0 !important;
        color: white !important;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #e9ecef !important;
        color: #473BF0 !important;
        font-weight: 600 !important;
    }

    /* Mobile Responsive Styles for Select2 */
    @media (max-width: 767px) {
        .select2-container .select2-selection--single {
            height: 42px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 42px !important;
            font-size: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
        }

        .select2-container--default .select2-results__option {
            padding: 8px 12px !important;
            font-size: 14px !important;
        }
    }

    /* Dark mode support */
    [data-theme='dark'] .select2-container .select2-selection--single {
        background-color: #2b2b2b !important;
        border-color: #3a3a3a !important;
    }

    /* Force Select2 styling - hide native select when Select2 is active */
    .select2-hidden-accessible {
        display: none !important;
    }

    /* Ensure Select2 container is visible and properly styled */
    .select2-container {
        display: block !important;
        width: 100% !important;
    }

    /* Fix for modal z-index issues */
    .select2-container--open {
        z-index: 9999 !important;
    }

    .select2-dropdown {
        z-index: 9999 !important;
    }

    [data-theme='dark'] .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #e9ecef !important;
    }

    [data-theme='dark'] .select2-dropdown {
        background-color: #2b2b2b !important;
        border-color: #3a3a3a !important;
    }

    [data-theme='dark'] .select2-container--default .select2-results__option {
        color: #e9ecef !important;
    }

    [data-theme='dark'] .select2-container--default .select2-search--dropdown .select2-search__field {
        background-color: #2b2b2b !important;
        border-color: #3a3a3a !important;
        color: #e9ecef !important;
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];

            // First get local user data
            $local_user_query = "SELECT * FROM user WHERE username = ?";
            $stmt = $conn->prepare($local_user_query);
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $local_user_result = $stmt->get_result();
            $local_user = $local_user_result->fetch_assoc();
            $stmt->close();

            // Check Appika API status for maintenance warnings
            include_once('../functions/appika-status-checker.php');
            $isAppikaOnline = isAppikaOnline();
            $maintenanceMessage = getAppikaStatusMessage();

            // Get complete customer data from both local database and Appika Contact API
            $user = getCompleteCustomerDataWithContact($username);

            if (!empty($user['has_appika_data']) && !empty($user['all_locations'])) {
                foreach ($user['all_locations'] as $loc) {
                    if (!empty($loc['is_primary_loc']) && $loc['is_primary_loc'] === 'y' && !empty($loc['state_name'])) {
                        $user['state_name'] = $loc['state_name'];
                        break;
                    }
                }
            }

            if ($user && $local_user) {
                // Merge local database fields that are not stored in Appika
                $user['company_name'] = $local_user['company_name'] ?? '';
                $user['tax_id'] = $local_user['tax_id'] ?? '';
                $user['id'] = $local_user['id'];
                $user['starter_tickets'] = $local_user['starter_tickets'] ?? 0;
                $user['premium_tickets'] = $local_user['premium_tickets'] ?? 0;
                $user['ultimate_tickets'] = $local_user['ultimate_tickets'] ?? 0;
            } elseif ($local_user) {
                // Fallback to local data if Appika data is not available
                $user = $local_user;
                $user['has_appika_data'] = false;
            }
            ?>
        <?php if ($user) :

                $sqlTickets = "SELECT SUM(remaining_tickets) AS total_remaining_tickets
                       FROM purchasetickets
                       WHERE username = '$username'
                       AND ticket_type = 'STARTER'
                       AND purchase_time >= CURDATE() - INTERVAL 1 YEAR;";

                $resultTickets = mysqli_query($conn, $sqlTickets);
                if ($resultTickets && mysqli_num_rows($resultTickets) > 0) :
                    $ticketStarterData = mysqli_fetch_assoc($resultTickets);
                    $total_starter_tickets = $ticketStarterData['total_remaining_tickets'] ?? 0;
                else :
                    $total_starter_tickets = 0;
                endif;

            ?>
        <!-- Display user data here -->

        <!-- Maintenance Warning Banner -->
        <?php if (!$isAppikaOnline): ?>
        <div class="alert alert-warning alert-dismissible fade show m-0" role="alert"
            style="border-radius: 0; border: none; background: linear-gradient(135deg, #ff9800, #f57c00); color: white; text-align: center; padding: 15px;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <div class="col-md-10">
                        <h5 class="mb-1"><strong>⚠️ Server Maintenance Notice</strong></h5>
                        <p class="mb-0">
                            <strong>Some profile data may not be available during maintenance.</strong>
                            Profile editing is temporarily disabled to prevent data loss.
                            You can still view your basic information below.
                        </p>
                        <small class="d-block mt-1">Status: <?php echo htmlspecialchars($maintenanceMessage); ?></small>
                    </div>
                    <div class="col-md-1 text-center">
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"
                            aria-label="Close"></button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Page Banner Area -->
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container"></div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">

                <div class="row justify-content-md-between pt-9">
                    <!-- <div class="col-sm-6 col-md-5 col-lg-4 col-xl-3">
                                <div class="btn btn-white border">PROFILE</div>
                            </div> -->
                    <div style="display: flex; flex-direction: row;" ;>
                        <div>
                            <!-- <div class="btn btn-white border"><strong>Tickets : </strong> <?php echo htmlspecialchars($total_starter_tickets); ?></div> -->
                        </div>
                        <!--
                                <div style="margin-left: 10px;">
                                    <div class="btn btn-white border"><strong>Business Tickets:</strong></div>
                                </div>
                                -->
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="cart-details-main-block" id="dynamic-cart">

                            <!-- .cart_single-product-block starts -->
                            <!-- style="margin-top: 20px;" -->
                            <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative card mb-4">
                                <?php if (isset($user)) : ?>
                                <div class="card-body">
                                    <div class="row justify-content-center pt-1 mb-4">
                                        <div class="col-12 text-center">
                                            <h2>Profile</h2>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Full name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 1. Full Name from Appika API
                                                if ($user['has_appika_data'] && !empty($user['name'])) {
                                                    echo htmlspecialchars($user['name']);
                                                } else {
                                                    echo htmlspecialchars($user['username'] ?? '');
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>

                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Email</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Phone</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 4. Phone from Appika API (tel_work)
                                                if ($user['has_appika_data'] && !empty($user['tel_work'])) {
                                                    echo htmlspecialchars($user['tel_work']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>

                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Company Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 5. Company Name from local database
                                                if (!empty($user['company_name'])) {
                                                    echo htmlspecialchars($user['company_name']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Tax ID</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 6. Tax ID from local database
                                                if (!empty($user['tax_id'])) {
                                                    echo htmlspecialchars($user['tax_id']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>

                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Address</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 7. Address from Appika API
                                                if ($user['has_appika_data'] && !empty($user['address'])) {
                                                    echo htmlspecialchars($user['address']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Address Notes</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 8. Address Notes (Address 2) from Appika API
                                                if ($user['has_appika_data'] && !empty($user['address2'])) {
                                                    echo htmlspecialchars($user['address2']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>

                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">City</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 10. City from Appika API
                                                if ($user['has_appika_data'] && !empty($user['city'])) {
                                                    echo htmlspecialchars($user['city']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">State/Province</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 11. State/Province from Appika API
                                                if ($user['has_appika_data'] && !empty($user['state_name'])) {
                                                    echo htmlspecialchars($user['state_name']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Country</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 12. Country from Appika API
                                                if ($user['has_appika_data'] && !empty($user['country'])) {
                                                    // Map country code to full name
                                                    $countries = [
                                                        'AF'=>'Afghanistan','AL'=>'Albania','DZ'=>'Algeria','AD'=>'Andorra','AO'=>'Angola','AR'=>'Argentina','AM'=>'Armenia','AU'=>'Australia','AT'=>'Austria','AZ'=>'Azerbaijan',
                                                        'BS'=>'Bahamas','BH'=>'Bahrain','BD'=>'Bangladesh','BB'=>'Barbados','BY'=>'Belarus','BE'=>'Belgium','BZ'=>'Belize','BJ'=>'Benin','BT'=>'Bhutan','BO'=>'Bolivia',
                                                        'BA'=>'Bosnia and Herzegovina','BW'=>'Botswana','BR'=>'Brazil','BN'=>'Brunei','BG'=>'Bulgaria','BF'=>'Burkina Faso','BI'=>'Burundi','CV'=>'Cabo Verde','KH'=>'Cambodia','CM'=>'Cameroon',
                                                        'CA'=>'Canada','CF'=>'Central African Republic','TD'=>'Chad','CL'=>'Chile','CN'=>'China','CO'=>'Colombia','KM'=>'Comoros','CG'=>'Congo','CR'=>'Costa Rica','HR'=>'Croatia',
                                                        'CU'=>'Cuba','CY'=>'Cyprus','CZ'=>'Czech Republic','DK'=>'Denmark','DJ'=>'Djibouti','DM'=>'Dominica','DO'=>'Dominican Republic','EC'=>'Ecuador','EG'=>'Egypt','SV'=>'El Salvador',
                                                        'GQ'=>'Equatorial Guinea','ER'=>'Eritrea','EE'=>'Estonia','SZ'=>'Eswatini','ET'=>'Ethiopia','FJ'=>'Fiji','FI'=>'Finland','FR'=>'France','GA'=>'Gabon','GM'=>'Gambia',
                                                        'GE'=>'Georgia','DE'=>'Germany','GH'=>'Ghana','GR'=>'Greece','GD'=>'Grenada','GT'=>'Guatemala','GN'=>'Guinea','GW'=>'Guinea-Bissau','GY'=>'Guyana','HT'=>'Haiti',
                                                        'HN'=>'Honduras','HU'=>'Hungary','IS'=>'Iceland','IN'=>'India','ID'=>'Indonesia','IR'=>'Iran','IQ'=>'Iraq','IE'=>'Ireland','IL'=>'Israel','IT'=>'Italy','JM'=>'Jamaica',
                                                        'JP'=>'Japan','JO'=>'Jordan','KZ'=>'Kazakhstan','KE'=>'Kenya','KI'=>'Kiribati','KW'=>'Kuwait','KG'=>'Kyrgyzstan','LA'=>'Laos','LV'=>'Latvia','LB'=>'Lebanon','LS'=>'Lesotho',
                                                        'LR'=>'Liberia','LY'=>'Libya','LI'=>'Liechtenstein','LT'=>'Lithuania','LU'=>'Luxembourg','MG'=>'Madagascar','MW'=>'Malawi','MY'=>'Malaysia','MV'=>'Maldives','ML'=>'Mali',
                                                        'MT'=>'Malta','MH'=>'Marshall Islands','MR'=>'Mauritania','MU'=>'Mauritius','MX'=>'Mexico','FM'=>'Micronesia','MD'=>'Moldova','MC'=>'Monaco','MN'=>'Mongolia','ME'=>'Montenegro',
                                                        'MA'=>'Morocco','MZ'=>'Mozambique','MM'=>'Myanmar','NA'=>'Namibia','NR'=>'Nauru','NP'=>'Nepal','NL'=>'Netherlands','NZ'=>'New Zealand','NI'=>'Nicaragua','NE'=>'Niger',
                                                        'NG'=>'Nigeria','KP'=>'North Korea','MK'=>'North Macedonia','NO'=>'Norway','OM'=>'Oman','PK'=>'Pakistan','PW'=>'Palau','PS'=>'Palestine','PA'=>'Panama','PG'=>'Papua New Guinea',
                                                        'PY'=>'Paraguay','PE'=>'Peru','PH'=>'Philippines','PL'=>'Poland','PT'=>'Portugal','QA'=>'Qatar','RO'=>'Romania','RU'=>'Russia','RW'=>'Rwanda','KN'=>'Saint Kitts and Nevis',
                                                        'LC'=>'Saint Lucia','VC'=>'Saint Vincent and the Grenadines','WS'=>'Samoa','SM'=>'San Marino','ST'=>'Sao Tome and Principe','SA'=>'Saudi Arabia','SN'=>'Senegal','RS'=>'Serbia',
                                                        'SC'=>'Seychelles','SL'=>'Sierra Leone','SG'=>'Singapore','SK'=>'Slovakia','SI'=>'Slovenia','SB'=>'Solomon Islands','SO'=>'Somalia','ZA'=>'South Africa','KR'=>'South Korea',
                                                        'SS'=>'South Sudan','ES'=>'Spain','LK'=>'Sri Lanka','SD'=>'Sudan','SR'=>'Suriname','SE'=>'Sweden','CH'=>'Switzerland','SY'=>'Syria','TW'=>'Taiwan','TJ'=>'Tajikistan',
                                                        'TZ'=>'Tanzania','TH'=>'Thailand','TL'=>'Timor-Leste','TG'=>'Togo','TO'=>'Tonga','TT'=>'Trinidad and Tobago','TN'=>'Tunisia','TR'=>'Turkey','TM'=>'Turkmenistan','TV'=>'Tuvalu',
                                                        'UG'=>'Uganda','UA'=>'Ukraine','AE'=>'United Arab Emirates','GB'=>'United Kingdom','US'=>'United States','UY'=>'Uruguay','UZ'=>'Uzbekistan','VU'=>'Vanuatu','VA'=>'Vatican City',
                                                        'VE'=>'Venezuela','VN'=>'Vietnam','YE'=>'Yemen','ZM'=>'Zambia','ZW'=>'Zimbabwe'
                                                    ];
                                                    $countryCode = $user['country'];
                                                    echo isset($countries[$countryCode]) ? $countries[$countryCode] : htmlspecialchars($countryCode);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Postal Code</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0">
                                                <?php
                                                // 13. Postal Code from Appika API
                                                if ($user['has_appika_data'] && !empty($user['postal_code'])) {
                                                    echo htmlspecialchars($user['postal_code']);
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                    <hr>
                                </div>
                                <style>
                                /* Center align the Edit Profile button */
                                .edit-profile-btn-container {
                                    text-align: center;
                                    margin: 15px auto;
                                    width: 100%;
                                    display: flex;
                                    flex-wrap: wrap;
                                    justify-content: center;
                                    gap: 10px;
                                }

                                .btn-primary.edit-profile-btn {
                                    display: inline-block;
                                    flex: 0 1 auto;
                                }

                                .btn-warning.reset-password-btn {
                                    display: inline-block;
                                    background-color: #ffc107;
                                    border-color: #ffc107;
                                    color: #212529;
                                    flex: 0 1 auto;
                                }

                                .btn-warning.reset-password-btn:hover {
                                    background-color: #e0a800;
                                    border-color: #d39e00;
                                    color: #212529;
                                }

                                /* Mobile responsive button styling */
                                @media (max-width: 767px) {
                                    .edit-profile-btn-container {
                                        flex-direction: column;
                                        align-items: center;
                                        gap: 15px;
                                    }

                                    .btn-primary.edit-profile-btn,
                                    .btn-warning.reset-password-btn {
                                        width: 100%;
                                        max-width: 280px;
                                        padding: 12px 20px;
                                        font-size: 16px;
                                        font-weight: 500;
                                        border-radius: 8px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        gap: 8px;
                                    }

                                    .btn-primary.edit-profile-btn i,
                                    .btn-warning.reset-password-btn i {
                                        font-size: 16px;
                                    }
                                }

                                /* Hide browser's default password visibility toggle */
                                input[type="password"]::-ms-reveal,
                                input[type="password"]::-ms-clear {
                                    display: none;
                                }

                                input[type="password"]::-webkit-credentials-auto-fill-button,
                                input[type="password"]::-webkit-strong-password-auto-fill-button {
                                    display: none !important;
                                }

                                /* Custom styling for password toggle buttons in reset modal */
                                #toggleNewPassword,
                                #toggleConfirmPassword {
                                    background-color: #473BF0 !important;
                                    border-color: #473BF0 !important;
                                    color: white !important;
                                    transition: all 0.3s ease;
                                }

                                #toggleNewPassword:hover,
                                #toggleConfirmPassword:hover {
                                    background-color: #3a2fd1 !important;
                                    border-color: #3a2fd1 !important;
                                    color: white !important;
                                    transform: scale(1.05);
                                }

                                #toggleNewPassword:focus,
                                #toggleConfirmPassword:focus {
                                    background-color: #473BF0 !important;
                                    border-color: #473BF0 !important;
                                    color: white !important;
                                    box-shadow: 0 0 0 0.2rem rgba(71, 59, 240, 0.25) !important;
                                }

                                #toggleNewPassword:active,
                                #toggleConfirmPassword:active {
                                    background-color: #3a2fd1 !important;
                                    border-color: #3a2fd1 !important;
                                    color: white !important;
                                }

                                /* Hide Edge/Chrome password reveal button */
                                input[type="password"]::-ms-reveal {
                                    display: none;
                                }

                                /* Hide Safari password reveal button */
                                input[type="password"]::-webkit-textfield-decoration-container {
                                    display: none;
                                }
                                </style>
                                <!-- .profile-block ends -->
                                <!-- Add this inside the card-body div -->
                                <div class="edit-profile-btn-container">
                                    <?php if ($isAppikaOnline): ?>
                                    <button type="button" class="btn btn-primary edit-profile-btn"
                                        data-bs-toggle="modal" data-bs-target="#editProfileModal">
                                        <i class="fas fa-edit"></i> Edit Profile
                                    </button>
                                    <?php else: ?>
                                    <button type="button" class="btn btn-secondary edit-profile-btn" disabled
                                        title="Profile editing is disabled during server maintenance to prevent data loss">
                                        <i class="fas fa-lock"></i> Edit Profile (Maintenance)
                                    </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-warning reset-password-btn" data-toggle="modal"
                                        data-target="#resetPasswordModal">
                                        <i class="fas fa-key"></i> Reset Password
                                    </button>
                                </div>
                                <style>
                                .btn-secondary {
                                    color: white;
                                    background-color: #68d585;
                                    border-color: #68d585;
                                }

                                /* Edit Profile Modal Styles */
                                #editProfileModal .modal-header {
                                    background-color: #473BF0 !important;
                                    color: white !important;
                                    border-bottom: 1px solid #3a30c0 !important;
                                }

                                #editProfileModal .modal-title {
                                    font-size: 22px !important;
                                    color: white !important;
                                    font-weight: 500 !important;
                                }

                                #editProfileModal .form-label {
                                    font-size: 16px !important;
                                    font-weight: 500 !important;
                                    color: #333 !important;
                                    margin-bottom: 8px !important;
                                }

                                /* Modal button styles - matching admin-ticket-detail.php */
                                .modal-btn-primary {
                                    background-color: #473BF0 !important;
                                    color: white !important;
                                    border: none !important;
                                    padding: 8px 16px !important;
                                    font-size: 14px !important;
                                    border-radius: 4px !important;
                                    transition: all 0.3s ease !important;
                                    min-width: 120px !important;
                                }

                                .modal-btn-primary:hover {
                                    background-color: #3a30c0 !important;
                                }

                                .modal-btn-cancel {
                                    background-color: #dc3545 !important;
                                    color: white !important;
                                    border: none !important;
                                    padding: 8px 16px !important;
                                    font-size: 14px !important;
                                    border-radius: 4px !important;
                                    transition: all 0.3s ease !important;
                                    min-width: 120px !important;
                                }

                                .modal-btn-cancel:hover {
                                    background-color: #c82333 !important;
                                }

                                @media (max-width: 789px) {
                                    .modal-footer {
                                        justify-content: center !important;
                                    }

                                    #editProfileModal .form-label {
                                        font-size: 14px !important;
                                    }

                                    .modal-btn-primary,
                                    .modal-btn-cancel {
                                        font-size: 13px !important;
                                        padding: 7px 14px !important;
                                        min-width: 100px !important;
                                    }
                                }

                                @media (max-width: 575px) {

                                    .modal-btn-primary,
                                    .modal-btn-cancel {
                                        font-size: 12px !important;
                                        padding: 6px 12px !important;
                                        min-width: 90px !important;
                                    }
                                }

                                /* Ensure both country and state dropdowns have consistent Select2 styling */
                                .select2-container {
                                    width: 100% !important;
                                    max-width: 100% !important;
                                }

                                .select2-container--default .select2-selection--single {
                                    height: 38px !important;
                                    border: 1px solid #ced4da !important;
                                    border-radius: 0.375rem !important;
                                    background-color: #fff !important;
                                    padding: 6px 12px !important;
                                }

                                .select2-container--default .select2-selection--single .select2-selection__rendered {
                                    line-height: 24px !important;
                                    padding-left: 0 !important;
                                    color: #495057 !important;
                                }

                                .select2-container--default .select2-selection--single .select2-selection__arrow {
                                    height: 36px !important;
                                    right: 10px !important;
                                }

                                .select2-dropdown {
                                    border: 1px solid #ced4da !important;
                                    border-radius: 0.375rem !important;
                                    z-index: 9999 !important;
                                }

                                .select2-container--default .select2-results__option {
                                    padding: 8px 12px !important;
                                }

                                .select2-container--default .select2-results__option--highlighted {
                                    background-color: #0d6efd !important;
                                    color: white !important;
                                }

                                /* Force Select2 styling on both dropdowns */
                                #country.select2-hidden-accessible+.select2-container,
                                #state.select2-hidden-accessible+.select2-container {
                                    display: block !important;
                                }

                                /* Hide original select elements when Select2 is active */
                                #country.select2-hidden-accessible,
                                #state.select2-hidden-accessible {
                                    display: none !important;
                                }
                                </style>
                                <!-- Modal for Edit Profile -->
                                <div class="modal fade modal-fullscreen-mobile" id="editProfileModal" tabindex="-1"
                                    aria-labelledby="editProfileModalLabel" aria-hidden="true">
                                    <div class="modal-dialog custom-width-modal">
                                        <div class="modal-content">
                                            <form method="post" action="../functions/edit_profile_appika.php"
                                                id="editProfileForm" onsubmit="return validateForm()">
                                                <div class="modal-header bg-primary">
                                                    <h5 class="modal-title text-white" id="editProfileModalLabel">Edit
                                                        Profile</h5>
                                                    <!-- <button type="button" class="btn-close close text-white"
                                                        data-bs-dismiss="modal" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button> -->
                                                </div>
                                                <div class="modal-body">
                                                    <!-- Username field -->
                                                    <!-- Hide the Customer Code field in the edit profile modal -->
                                                    <!--
                                                    <div class="mb-3">
                                                        <label for="username" class="form-label">Customer Code *</label>
                                                        <input type="text" class="form-control" id="username"
                                                            name="username"
                                                            value="<?php echo htmlspecialchars($user['appika_id'] ?? 'Not assigned'); ?>"
                                                            required readonly
                                                            style="background-color: #f8f9fa; cursor: not-allowed;">
                                                        <small class="form-text text-muted">Customer Code cannot be changed for security reasons.</small>
                                                    </div>
                                                    -->

                                                    <!-- Full Name -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="first_name" class="form-label">Full name
                                                                *</label>
                                                            <input type="text" class="form-control" id="first_name"
                                                                name="first_name"
                                                                value="<?php echo htmlspecialchars($user['has_appika_data'] && !empty($user['name']) ? $user['name'] : ($user['username'] ?? '')); ?>"
                                                                required>
                                                        </div>
                                                    </div>

                                                    <!-- Email and Phone on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="email" class="form-label">Email *</label>
                                                            <input type="email" class="form-control" id="email"
                                                                name="email"
                                                                value="<?php echo htmlspecialchars($user['email']); ?>"
                                                                required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="tell" class="form-label">Phone
                                                                (Optional)</label>
                                                            <input type="text" class="form-control" id="tell"
                                                                name="tell"
                                                                value="<?php echo htmlspecialchars($user['has_appika_data'] && !empty($user['tel_work']) ? $user['tel_work'] : ''); ?>">
                                                        </div>
                                                    </div>

                                                    <!-- Company Name and Tax ID on same line -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="company_name" class="form-label">Company Name
                                                                (Optional)</label>
                                                            <input type="text" class="form-control" id="company_name"
                                                                name="company_name"
                                                                value="<?php echo htmlspecialchars($user['company_name'] ?? ''); ?>">
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="tax_id" class="form-label">Tax ID
                                                                (Optional)</label>
                                                            <input type="text" class="form-control" id="tax_id"
                                                                name="tax_id"
                                                                value="<?php echo htmlspecialchars($user['tax_id'] ?? ''); ?>">
                                                        </div>
                                                    </div>



                                                    <!-- Address field -->
                                                    <div class="mb-3">
                                                        <label for="address" class="form-label">Address *</label>
                                                        <textarea class="form-control" id="address" name="address"
                                                            rows="2"
                                                            placeholder="Enter your address"><?php echo htmlspecialchars($user['has_appika_data'] && !empty($user['address']) ? $user['address'] : ''); ?></textarea>
                                                    </div>

                                                    <!-- Address Notes field -->
                                                    <div class="mb-3">
                                                        <label for="address2" class="form-label">Address 2
                                                            (Optional)</label>
                                                        <textarea class="form-control" id="address2" name="address2"
                                                            rows="3"
                                                            placeholder="Enter any additional notes about your address"><?php echo htmlspecialchars($user['has_appika_data'] && !empty($user['address2']) ? $user['address2'] : ''); ?></textarea>
                                                    </div>

                                                    <!-- Country and State/Province fields in modal edit profile -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="country" class="form-label">Country *</label>
                                                            <select class="form-control" id="country" name="country"
                                                                required style="width: 100%; background-image: none;">
                                                                <option value="">Loading countries...</option>
                                                                <option value="">Select Country</option>
                                                                <?php
                                                                // Load countries dynamically using geographer
                                                                require_once __DIR__ . '/../functions/state-mapping.php';

                                                                try {
                                                                    $countries = getCountriesMapping();
                                                                    $selectedCountry = $user['has_appika_data'] && !empty($user['country']) ? $user['country'] : '';

                                                                    if (!empty($countries)) {
                                                                        foreach ($countries as $country) {
                                                                            $selected = ($selectedCountry === $country['code']) ? 'selected' : '';
                                                                            echo "<option value=\"{$country['code']}\" $selected>{$country['name']}</option>";
                                                                        }
                                                                    } else {
                                                                        echo '<option value="">No countries available</option>';
                                                                    }
                                                                } catch (Exception $e) {
                                                                    error_log("Error loading countries: " . $e->getMessage());
                                                                    echo '<option value="">Error loading countries</option>';
                                                                }
                                                                ?>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="state" class="form-label">State/Province
                                                                *</label>
                                                            <select class="form-control" id="state" name="state"
                                                                required style="width: 100%;">
                                                                <option value="">Select Country First</option>
                                                                <!-- States will be loaded dynamically -->
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <!-- Postal Code field (make wider) -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label for="postal_code" class="form-label">Postal Code
                                                                *</label>
                                                            <input type="text" class="form-control" id="postal_code"
                                                                name="postal_code"
                                                                value="<?php echo htmlspecialchars($user['has_appika_data'] && !empty($user['postal_code']) ? $user['postal_code'] : ''); ?>"
                                                                required
                                                                style="min-width:180px; width:100%; max-width:100%;">
                                                        </div>
                                                        <div class="col-md-6"></div>
                                                    </div>

                                                </div>
                                                <div class="modal-footer justify-content-center">
                                                    <button type="submit" class="btn modal-btn-primary">Save
                                                        changes</button>
                                                    <button type="button" class="btn modal-btn-cancel"
                                                        data-bs-dismiss="modal" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php else : ?>
                                <p><?php echo $error_message; ?></p>
                                <?php endif; ?>

                            </div>
                            <!-- .cart_single-product-block ends -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "No user found."; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "You are not logged in."; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <!-- Form Validation Script -->
    <script>
    function validateForm() {
        // Get form values
        const username = document.getElementById('username').value.trim();
        const phoneNumber = document.getElementById('tell').value.trim();
        const taxId = document.getElementById('tax_id').value.trim();
        const postalCode = document.getElementById('postal_code').value.trim();

        // Validate username (must contain only letters, numbers, and underscores)
        if (username.length === 0) {
            showAlert('Username is required.');
            return false;
        }

        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            showAlert('Username must contain only letters, numbers, and underscores.');
            return false;
        }

        if (username.length < 3) {
            showAlert('Username must be at least 3 characters long.');
            return false;
        }

        if (username.length > 50) {
            showAlert('Username must be no more than 50 characters long.');
            return false;
        }

        // Validate phone number (optional, but if provided must be 10 digits)
        if (phoneNumber.length !== 0) {
            // Remove any non-digit characters for validation
            const digitsOnly = phoneNumber.replace(/\D/g, '');

            if (digitsOnly.length !== 10) {
                showAlert('Phone number must be exactly 10 digits (if provided).');
                return false;
            }
        }

        // Validate Tax ID (must contain only numbers if provided)
        if (taxId.length !== 0 && !/^\d+$/.test(taxId)) {
            showAlert('Tax ID must contain only numbers.');
            return false;
        }

        // Validate Postal Code (optional, but if provided must contain only numbers)
        if (postalCode.length !== 0 && !/^\d+$/.test(postalCode)) {
            showAlert('Postal Code must contain only numbers (if provided).');
            return false;
        }

        // If all validations pass
        return true;
    }

    // Function to show custom alert
    function showAlert(message, title = 'Warning') {
        document.getElementById('alertTitle').textContent = title;
        document.getElementById('alertMessage').textContent = message;
        document.getElementById('alertOverlay').classList.add('show');
        document.getElementById('customAlert').classList.add('show');
    }

    // Function to close custom alert
    function closeAlert() {
        document.getElementById('alertOverlay').classList.remove('show');
        document.getElementById('customAlert').classList.remove('show');
    }

    // Show success modal on page load if needed
    <?php if (isset($_GET['success']) && $_GET['success'] == 1) : ?>
    $(document).ready(function() {
        $('#successModal').modal('show');

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href.split('?')[0]);
        }

        // Start countdown for automatic redirect
        var countdownElement = document.getElementById('countdown');
        var secondsLeft = 3;

        // Update countdown every second
        var countdownInterval = setInterval(function() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            if (secondsLeft <= 0) {
                clearInterval(countdownInterval);
                window.location.href = 'profile';
            }
        }, 1000);
    });
    <?php endif; ?>

    <?php if (isset($_GET['error'])) : ?>
    $(document).ready(function() {
        <?php if ($_GET['error'] == 2) : ?>
        // Profile update error - show specific errors
        <?php if (isset($_SESSION['profile_errors']) && !empty($_SESSION['profile_errors'])) : ?>
        let errorMessages = <?php echo json_encode($_SESSION['profile_errors']); ?>;
        let errorText = errorMessages.join('<br>');
        $('#errorMessage').html(errorText);
        <?php unset($_SESSION['profile_errors']); ?>
        <?php endif; ?>
        $('#errorModal').modal('show');
        <?php elseif ($_GET['error'] == 3) : ?>
        // Username already exists error
        $('#usernameExistsModal').modal('show');

        // Focus on username field when modal is closed
        $('#usernameExistsOkBtn, #usernameExistsModal .close').on('click', function() {
            setTimeout(function() {
                $('#username').focus().select();
            }, 500);
        });
        <?php else : ?>
        // General error
        $('#errorModal').modal('show');
        <?php endif; ?>

        // Prevent form resubmission on refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href.split('?')[0]);
        }
    });
    <?php endif; ?>
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="success-animation">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h3 class="mt-4">Profile Updated Successfully!</h3>
                    <p class="mb-4">Your profile information has been updated.</p>
                    <p class="text-muted">This window will close automatically in <span id="countdown">3</span> seconds.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center py-5">
                    <div class="text-center text-danger mb-4">
                        <i class="fas fa-exclamation-circle" style="font-size: 60px;"></i>
                    </div>
                    <h3 class="mt-2">Error!</h3>
                    <p class="mb-4" id="errorMessage">There was an error updating your profile. Please try again.</p>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Username Exists Modal -->
    <div class="modal fade" id="usernameExistsModal" tabindex="-1" role="dialog" aria-hidden="true"
        data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white" id="usernameExistsModalLabel" style="font-size: 22px;">Username
                        Already
                        Exists</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle text-danger" style="font-size: 60px;"></i>
                    </div>
                    <h4>Update Failed</h4>
                    <p class="mb-4">The username you entered is already taken by another account. Please choose a
                        different username.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-dismiss="modal"
                        id="usernameExistsOkBtn">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog"
        aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resetPasswordModalLabel">
                        <i class="fas fa-key mr-2"></i>Reset Password
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <div class="form-group">
                            <label for="resetEmail">Email Address</label>
                            <input type="email" class="form-control" id="resetEmail" name="email"
                                value="<?php echo htmlspecialchars($user['email']); ?>" readonly autocomplete="off"
                                style="background-color: #f8f9fa; cursor: not-allowed;">

                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-secondary w-100" id="send2FACodeBtn"
                                style="color: white; background-color: #473BF0;">Send 2FA Code</button>
                        </div>
                        <div class="form-group" id="codeGroup" style="display:none;">
                            <label for="reset2FACode">2FA Code</label>
                            <input type="text" class="form-control" id="reset2FACode" name="code"
                                placeholder="Enter the 2FA code" maxlength="6">
                        </div>
                        <div class="form-group" id="passwordGroup" style="display:none;">
                            <label for="newPassword">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="newPassword" name="new_password"
                                    placeholder="Enter new password" minlength="6">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword"><i
                                            class="fas fa-eye-slash"></i></button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                        </div>
                        <div class="form-group" id="confirmPasswordGroup" style="display:none;">
                            <label for="confirmPassword">Confirm New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password"
                                    placeholder="Confirm new password">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button"
                                        id="toggleConfirmPassword"><i class="fas fa-eye-slash"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="updatePasswordGroup" style="display:none;">
                            <button type="button" class="btn btn-primary w-100" id="resetPasswordBtn"
                                style="color: white; background-color: #473BF0;">Update

                                Password</button>
                        </div>
                        <div id="resetMessage" class="alert" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-primary" id="resetPasswordBtn">
                        <i class="fas fa-sync-alt mr-2"></i>Reset Password
                    </button> -->
                </div>
            </div>
        </div>
    </div>
    <script>
    // Debug the modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Get the button and modal elements
        const editProfileBtn = document.querySelector(
            '[data-bs-toggle="modal"][data-bs-target="#editProfileModal"]');
        const editProfileModal = document.getElementById('editProfileModal');

        if (editProfileBtn && editProfileModal) {
            console.log('Edit profile button and modal found');

            // Add a click event listener to the button
            editProfileBtn.addEventListener('click', function() {
                console.log('Edit profile button clicked');

                // Check if Bootstrap modal is available
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    console.log('Bootstrap Modal is available');
                    const modal = new bootstrap.Modal(editProfileModal);
                    modal.show();
                } else {
                    console.error('Bootstrap Modal is not available');
                    // Fallback - try jQuery if available
                    if (typeof $ !== 'undefined' && typeof $('#editProfileModal').modal ===
                        'function') {
                        console.log('Using jQuery modal fallback');
                        $('#editProfileModal').modal('show');
                    } else {
                        console.error('Neither Bootstrap nor jQuery modal functionality is available');
                    }
                }
            });
        } else {
            console.error('Edit profile button or modal not found');
            if (!editProfileBtn) console.error('Button not found');
            if (!editProfileModal) console.error('Modal not found');
        }

        // Reset Password Modal Functions
        const toggleNewPasswordBtn = document.getElementById('toggleNewPassword');
        const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPassword');
        const resetPasswordBtn = document.getElementById('resetPasswordBtn');

        if (toggleNewPasswordBtn) {
            toggleNewPasswordBtn.addEventListener('click', function() {
                var passwordField = document.getElementById('newPassword');
                var icon = this.querySelector('i');

                // Toggle the eye icon (same logic as main login)
                icon.classList.toggle('fa-eye-slash');
                icon.classList.toggle('fa-eye');

                // Toggle the password field type
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                } else {
                    passwordField.type = 'password';
                }
            });
        }

        if (toggleConfirmPasswordBtn) {
            toggleConfirmPasswordBtn.addEventListener('click', function() {
                var passwordField = document.getElementById('confirmPassword');
                var icon = this.querySelector('i');

                // Toggle the eye icon (same logic as main login)
                icon.classList.toggle('fa-eye-slash');
                icon.classList.toggle('fa-eye');

                // Toggle the password field type
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                } else {
                    passwordField.type = 'password';
                }
            });
        }

        if (resetPasswordBtn) {
            resetPasswordBtn.addEventListener('click', function() {
                var email = document.getElementById('resetEmail').value;
                var code = document.getElementById('reset2FACode').value;
                var newPassword = document.getElementById('newPassword').value;
                var confirmPassword = document.getElementById('confirmPassword').value;
                var resetBtn = this;
                if (!email || !code || !newPassword || !confirmPassword) {
                    showResetMessage('Please fill in all fields.', 'danger');
                    return;
                }
                if (newPassword !== confirmPassword) {
                    showResetMessage('Passwords do not match.', 'danger');
                    return;
                }
                if (newPassword.length < 6) {
                    showResetMessage('Password must be at least 6 characters long.', 'danger');
                    return;
                }
                resetBtn.disabled = true;
                resetBtn.innerHTML = 'Updating...';
                fetch('../functions/reset-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: 'action=reset_password&email=' + encodeURIComponent(email) +
                            '&code=' + encodeURIComponent(code) + '&new_password=' +
                            encodeURIComponent(newPassword)
                    })
                    .then(response => response.json())
                    .then(data => {
                        resetBtn.disabled = false;
                        resetBtn.innerHTML = 'Update Password';
                        showResetMessage(data.message, data.success ? 'success' : 'danger');
                        if (data.success) {
                            document.getElementById('resetPasswordForm').reset();
                            document.getElementById('resetEmail').value =
                                '<?php echo htmlspecialchars($user['email']); ?>';
                            setTimeout(function() {
                                $('#resetPasswordModal').modal('hide');
                            }, 3000);
                        }
                    })
                    .catch(() => {
                        resetBtn.disabled = false;
                        resetBtn.innerHTML = 'Update Password';
                        showResetMessage('Network error. Please try again.', 'danger');
                    });
            });
        }

        // Reset Password Message Function
        window.showResetMessage = function(message, type) {
            var messageDiv = document.getElementById('resetMessage');
            if (messageDiv) {
                messageDiv.className = 'alert alert-' + type;
                messageDiv.innerHTML = message;
                messageDiv.style.display = 'block';

                // Auto-hide success and warning messages
                if (type === 'success' || type === 'warning') {
                    setTimeout(function() {
                        messageDiv.style.display = 'none';
                    }, 5000);
                }
            }
        };
    });

    // Global variable to track cooldown timer for profile page
    var profileCooldownTimer = null;

    var send2FACodeBtn = document.getElementById('send2FACodeBtn');
    if (send2FACodeBtn) {
        send2FACodeBtn.addEventListener('click', function() {
            var email = document.getElementById('resetEmail').value;
            send2FACodeBtn.disabled = true;
            send2FACodeBtn.innerHTML = 'Sending...';
            fetch('../functions/reset-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'action=send_2fa&email=' + encodeURIComponent(email)
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                })
                .catch(() => {
                    alert('Failed to send 2FA code. Please try again.');
                })
                .finally(() => {
                    send2FACodeBtn.disabled = false;
                    send2FACodeBtn.innerHTML = 'Send 2FA Code';
                });
        });
    }
    </script>

    <!-- Ensure jQuery is loaded before Select2 -->
    <script src="../plugins/jquery/jquery.min.js"></script>
    <!-- Try local Select2, fallback to CDN if not found -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    if (typeof $.fn.select2 === 'undefined') {
        document.write(
            '<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"><\/script>');
    }
    </script>
    <script>
    $(document).ready(function() {
        // Existing Select2 and other code...
        $('#editProfileForm').on('submit', function() {
            // Hide the modal immediately on submit
            $('#editProfileModal').modal('hide');
            // Let the form submit normally (page will reload)
        });

        // Robust fix for lingering modal backdrop/black screen
        $('#editProfileModal').on('hidden.bs.modal', function() {
            setTimeout(function() {
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                $('body').css('padding-right', '');
            }, 100); // Delay to ensure Bootstrap has finished hiding
        });
    });
    </script>
    <!-- Ensure Bootstrap JS is loaded for modal functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    if (typeof $().modal !== 'function') {
        document.write(
            '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"><\/script>');
    }
    </script>
</body>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var send2FACodeBtn = document.getElementById('send2FACodeBtn');
    if (send2FACodeBtn) {
        send2FACodeBtn.addEventListener('click', function() {
            var email = document.getElementById('resetEmail').value;
            send2FACodeBtn.disabled = true;
            send2FACodeBtn.innerHTML = 'Sending...';
            fetch('../functions/reset-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'action=send_2fa&email=' + encodeURIComponent(email)
                })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                })
                .catch(() => {
                    alert('Failed to send 2FA code. Please try again.');
                })
                .finally(() => {
                    send2FACodeBtn.disabled = false;
                    send2FACodeBtn.innerHTML = 'Send 2FA Code';
                });
        });
    }
});
</script>

<!-- Before the closing </body> tag, add this script -->
<script>
$(document).ready(function() {
    console.log('Document ready - checking Select2...');
    console.log('jQuery version:', $.fn.jquery);
    console.log('Select2 available:', typeof $.fn.select2 !== 'undefined');

    // Initialize Select2 when modal is shown (not on document ready)
    $('#editProfileModal').on('shown.bs.modal', function() {
        console.log('Modal shown - initializing Select2...');

        // Check if Select2 is available
        if (typeof $.fn.select2 === 'undefined') {
            console.error('Select2 is not loaded!');
            return;
        }

        try {
            // Destroy existing Select2 instances if they exist
            $('#country, #state').each(function() {
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2('destroy');
                }
            });

            // Initialize Select2 for country and state dropdowns
            $('#country, #state').each(function() {
                const $this = $(this);
                const elementId = $this.attr('id');

                console.log('Initializing Select2 for:', elementId);

                $this.select2({
                    width: '100%',
                    dropdownParent: $('#editProfileModal'),
                    minimumResultsForSearch: 10,
                    closeOnSelect: true,
                    placeholder: elementId === 'country' ? 'Select Country' : 'Select State/Province',
                    allowClear: false,
                    escapeMarkup: function(markup) {
                        return markup;
                    }
                });

                console.log('Select2 initialized for:', elementId);
            });

            console.log('All Select2 dropdowns initialized successfully');

        } catch (error) {
            console.error('Error initializing Select2:', error);
        }
    });

    // Clean up when modal is hidden
    $('#editProfileModal').on('hidden.bs.modal', function() {
        console.log('Modal hidden - cleaning up...');

        // Close any open Select2 dropdowns
        $('#country, #state').each(function() {
            if ($(this).hasClass('select2-hidden-accessible')) {
                $(this).select2('close');
            }
        });
    });

    // Function to load states for selected country
    function loadStates(countryCode, selectedState = '') {
        const stateSelect = $('#state');
        stateSelect.html('<option value="">Loading states...</option>');

        $.ajax({
            url: '../api/get-states.php',
            method: 'GET',
            data: {
                country: countryCode
            },
            success: function(response) {
                console.log('States API Response:', response);

                if (response.success && response.states && response.states.length > 0) {
                    let options = '<option value="">Select State/Province</option>';
                    response.states.forEach(function(state) {
                        // Use ISO code as value and display local name with English name if different
                        const selected = (state.value === selectedState || state.text ===
                            selectedState) ? 'selected' : '';
                        const displayName = state.local_name && state.local_name !== state
                            .text ?
                            `${state.local_name} — ${state.text}` :
                            state.text;
                        options +=
                            `<option value="${state.value}" ${selected}>${displayName}</option>`;
                    });
                    stateSelect.html(options);
                } else if (response.success && response.states && response.states.length === 0) {
                    stateSelect.html('<option value="">Not applicable for this country</option>');
                } else {
                    stateSelect.html('<option value="">Not applicable for this country</option>');
                }

                // Ensure Select2 is properly applied after loading new options
                stateSelect.trigger('change.select2');

                // Re-initialize Select2 if needed
                if (!stateSelect.hasClass('select2-hidden-accessible')) {
                    stateSelect.select2({
                        width: '100%',
                        dropdownParent: $('#editProfileModal'),
                        minimumResultsForSearch: 10,
                        closeOnSelect: true,
                        placeholder: 'Select State/Province',
                        allowClear: false
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('States API Error:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
                stateSelect.html('<option value="">Error loading states</option>');
            }
        });
    }

    // Load states when country changes
    $('#country').on('change', function() {
        const countryCode = $(this).val();
        if (countryCode) {
            loadStates(countryCode);
        } else {
            $('#state').html('<option value="">Select Country First</option>').trigger(
                'change.select2');
        }
    });

    // Initial load of states if country is pre-selected
    const initialCountry = $('#country').val();
    // Try to get state from different possible fields (ISO code preferred, then state name)
    const initialState = '<?php
        $state_value = '';
        if ($user['has_appika_data']) {
            // Try to get state ISO code first, then fall back to state name
            if (!empty($user['state'])) {
                $state_value = $user['state']; // This might be ISO code
            } elseif (!empty($user['state_name'])) {
                $state_value = $user['state_name']; // This is the English name
            }
        }
        echo addslashes($state_value);
    ?>';
    if (initialCountry) {
        loadStates(initialCountry, initialState);
    }
});
</script>

</html>