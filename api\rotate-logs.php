<?php
// Rotate large log files

$logFile = '../logs/ticket_api.log';

if (!file_exists($logFile)) {
    echo "❌ Log file not found: {$logFile}\n";
    exit;
}

$fileSize = filesize($logFile);
$maxSize = 5 * 1024 * 1024; // 5MB

echo "📊 Current log file size: " . formatBytes($fileSize) . "\n";

if ($fileSize > $maxSize) {
    $backupFile = '../logs/ticket_api_' . date('Y-m-d_H-i-s') . '.log';
    
    // Move current log to backup
    if (rename($logFile, $backupFile)) {
        echo "✅ Log rotated successfully!\n";
        echo "📁 Backup created: {$backupFile}\n";
        echo "🆕 New log file will be created on next ticket creation\n";
    } else {
        echo "❌ Failed to rotate log file\n";
    }
} else {
    echo "ℹ️ Log file size is acceptable (under " . formatBytes($maxSize) . ")\n";
    echo "🔄 No rotation needed\n";
}

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
