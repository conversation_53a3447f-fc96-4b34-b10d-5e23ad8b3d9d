<?php
function getCustomerContacts($customerId) {
    $conn = new mysqli("localhost", "your_username", "your_password", "your_database");
    $contacts = [];
    
    if ($conn->connect_error) {
        return ['error' => 'Database connection failed: ' . $conn->connect_error];
    }
    
    $stmt = $conn->prepare("SELECT * FROM customer_contacts WHERE customer_id = ?");
    $stmt->bind_param("s", $customerId);
    
    if ($stmt->execute()) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $contacts[] = $row;
        }
    }
    
    $stmt->close();
    $conn->close();
    
    return $contacts;
}
