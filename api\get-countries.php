<?php
/**
 * API endpoint to get all countries using menarasolutions/geographer
 * Returns countries with ISO codes for the profile dropdown
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../functions/state-mapping.php';

try {
    // Get countries mapping using the existing function
    $countries = getCountriesMapping();
    
    if (empty($countries)) {
        echo json_encode([
            'success' => false,
            'error' => 'No countries found'
        ]);
        exit;
    }
    
    // Format countries for dropdown
    $formattedCountries = [];
    foreach ($countries as $country) {
        $formattedCountries[] = [
            'value' => $country['code'], // ISO 2-letter code (e.g., 'US', 'TH')
            'text' => $country['name']   // Country name (e.g., 'United States', 'Thailand')
        ];
    }
    
    // Return successful response
    echo json_encode([
        'success' => true,
        'countries' => $formattedCountries
    ]);
    
} catch (Exception $e) {
    error_log("Get countries API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load countries: ' . $e->getMessage()
    ]);
}
?>