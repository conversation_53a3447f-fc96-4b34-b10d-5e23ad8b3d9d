<?php
/**
 * Core API POST & PUT Example - Using Guzzle to connect to the API
 *
 * This example demonstrates how to use POST and PUT methods with the API endpoint:
 * https://api-sgsg.appika.com/contact/customers
 * using the Core API (REST) approach with Guzzle HTTP client.
 */

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load centralized API configuration
require_once __DIR__ . '/../config/api-config.php';

// Include state mapping functions
require_once __DIR__ . '/../functions/state-mapping.php';

// Start session to persist data across requests
session_start();

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];

// Store API key in session for persistence
$tokenMessage = '';
$tokenMessageType = '';
if (isset($_POST['api_token']) && !empty($_POST['api_token'])) {
    $_SESSION['apiKey'] = $_POST['api_token'];
    // Validate token by making a simple API call
    $testApiKey = $_POST['api_token'];
    $testClient = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 10,
        'http_errors' => false,
    ]);
    try {
        $testResponse = $testClient->request('GET', $apiPath, [
            'headers' => [
                'X-api-key' => "{$testApiKey}",
                'Accept' => 'application/json',
            ],
            'query' => ['limit' => 1]
        ]);
        $testStatus = $testResponse->getStatusCode();
        if ($testStatus == 401 || $testStatus == 403) {
            $tokenMessage = 'Token expired or incorrect. Please check your token.';
            $tokenMessageType = 'danger';
            unset($_SESSION['apiKey']);
        } elseif ($testStatus >= 200 && $testStatus < 300) {
            $tokenMessage = 'Token updated successfully.';
            $tokenMessageType = 'success';
        } else {
            $tokenMessage = 'Token validation failed (HTTP ' . $testStatus . ').';
            $tokenMessageType = 'warning';
        }
    } catch (\Exception $e) {
        $tokenMessage = 'Token validation error: ' . $e->getMessage();
        $tokenMessageType = 'danger';
        unset($_SESSION['apiKey']);
    }
}
if (isset($_SESSION['apiKey']) && !empty($_SESSION['apiKey'])) {
    $apiKey = $_SESSION['apiKey'];
} else {
    // Use centralized API key as default
    $apiKey = $apiConfig['key'];
}

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $apiEndpoint,
    'timeout' => 30,
    'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
]);

// Function to display results in a readable format
function displayResults($title, $data) {
    echo "<h2>{$title}</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Function to handle API requests and display results
function makeApiRequest($client, $method, $endpoint, $options = [], $title = '') {
    try {
        // Display request URL and headers for debugging
        echo "<h3>Request URL (Debug):</h3>";
        echo "<pre>{$method} {$endpoint}</pre>";

        if (isset($options['headers'])) {
            echo "<h3>Request Headers (Debug):</h3>";
            echo "<pre>";
            print_r($options['headers']);
            echo "</pre>";
        }

        if (isset($options['json'])) {
            echo "<h3>Request Data (Debug):</h3>";
            echo "<pre>";
            print_r($options['json']);
            echo "</pre>";
        }

        // Send the request
        $response = $client->request($method, $endpoint, $options);

        // Get status code
        $statusCode = $response->getStatusCode();
        echo "<p>Status Code: {$statusCode}</p>";

        // Get response body
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            displayResults($title . ' Response (JSON):', $data);
        } else {
            displayResults($title . ' Response:', $body);
        }

        return [
            'status' => $statusCode,
            'data' => $data ?? $body
        ];
    } catch (\Exception $e) {
        echo "<h2>Error Occurred</h2>";
        echo "<p>Error Message: " . $e->getMessage() . "</p>";
        return [
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Process form submission
$message = '';
$messageType = '';
$customerId = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        // Common headers for all requests
        $headers = [
            'X-api-key' => "{$apiKey}",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];

        switch ($action) {
            case 'create':
                // Create a new customer
                $customerNo = !empty($_POST['customerNo']) ? $_POST['customerNo'] : 'CUST' . rand(1000, 9999);
                $customerName = !empty($_POST['customerName']) ? $_POST['customerName'] : 'New Customer';
                $customerEntityType = !empty($_POST['customerEntityType']) ? $_POST['customerEntityType'] : 'COMPANY';
                $customerGroup = !empty($_POST['customerGroup']) ? $_POST['customerGroup'] : '10'; // Use numeric group id
                $customerStartDate = !empty($_POST['customerStartDate']) ? $_POST['customerStartDate'] : date('Y-m-d');
                $status = !empty($_POST['status']) ? $_POST['status'] : 'a';

                // Add required fields: grp_id, ofc_id, assign2, creator
                $ofcId = '511';
                $assignTo = '1';
                $creator = '1';

                $customerData = [
                    'no' => $customerNo,
                    'name' => $customerName,
                    'entity_type' => $customerEntityType === 'COMPANY' ? '1' : '2',
                    'grp_id' => $customerGroup, // Use grp_id and set to a valid numeric group id
                    'ofc_id' => $ofcId,
                    'assign2' => $assignTo,
                    'creator' => $creator,
                    'start_date' => $customerStartDate,
                    'status' => $status
                ];

                $result = makeApiRequest(
                    $client,
                    'POST',
                    $apiPath,
                    [
                        'headers' => $headers,
                        'json' => $customerData
                    ],
                    'Create Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Customer created successfully!';
                    $messageType = 'success';
                    $customerId = $result['data']['id'] ?? '';
                } else {
                    $message = 'Failed to create customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'update':
                // Update an existing customer
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required for update.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];
                $customerName = !empty($_POST['customerName']) ? $_POST['customerName'] : 'Updated Customer';
                $customerEntityType = !empty($_POST['customerEntityType']) ? $_POST['customerEntityType'] : 'COMPANY';
                $customerGroup = !empty($_POST['customerGroup']) ? $_POST['customerGroup'] : '10';
                $customerStartDate = !empty($_POST['customerStartDate']) ? $_POST['customerStartDate'] : date('Y-m-d');
                $status = !empty($_POST['status']) ? $_POST['status'] : 'a';

                // Add required fields: grp_id, ofc_id, assign2, creator (like Create)
                $ofcId = '511';
                $assignTo = '1';
                $creator = '1';

                // Get the current customer data
                echo "<h3>Getting current customer data for update...</h3>";
                $getResult = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Get Customer for Update'
                );

                if ($getResult['status'] >= 200 && $getResult['status'] < 300 &&
                    isset($getResult['data']['items']) && !empty($getResult['data']['items'])) {

                    $currentCustomer = $getResult['data']['items'][0];
                    echo "<p>Current customer data retrieved successfully.</p>";

                    // Use the same structure as Create (POST)
                    $customerData = [
                        'no' => $currentCustomer['no'],
                        'name' => $customerName,
                        'entity_type' => $customerEntityType === 'COMPANY' ? '1' : '2',
                        'grp_id' => $customerGroup,
                        'ofc_id' => $ofcId,
                        'assign2' => $assignTo,
                        'creator' => $creator,
                        'start_date' => $customerStartDate,
                        'status' => $status
                    ];

                    echo "<h3>Update data:</h3>";
                    echo "<pre>";
                    print_r($customerData);
                    echo "</pre>";

                } else {
                    $message = 'Failed to get current customer data for update.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $currentCustomer['id'];

                echo "<h3>Update URL:</h3>";
                echo "<pre>{$apiPath}/{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'PUT',
                    $apiPath . '/' . $customerId,
                    [
                        'headers' => $headers,
                        'json' => $customerData
                    ],
                    'Update Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Customer updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;



            case 'search_for_update':
                // Search for a customer to update
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                // Display the customer ID being searched
                echo "<h3>Searching for Customer to Update:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            // Search specifically by the 'no' field (customer number)
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Update'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    // Check if we got any results
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer found! You can now update the details.';
                        $messageType = 'success';

                        // Store the customer data for the update form
                        $customerForUpdate = $result['data']['items'][0];

                        // Use actual data if available, fallback to empty/defaults
                        $primary = isset($customerForUpdate['primary_location']) && is_array($customerForUpdate['primary_location'])
                            ? $customerForUpdate['primary_location']
                            : [];

                        $customerForUpdate['primary_location'] = [
                            'id' => $primary['id'] ?? ($customerForUpdate['id'] ?? ''),
                            'status' => $primary['status'] ?? ($customerForUpdate['status'] ?? 'a'),
                            'is_leaf' => $primary['is_leaf'] ?? 'y',
                            'loc_code' => $primary['loc_code'] ?? ('CUST-' . ($customerForUpdate['id'] ?? rand(100, 999))),
                            'loc_name' => $primary['loc_name'] ?? (($customerForUpdate['name'] ?? 'Location') . ' Location'),
                            'is_primary_loc' => $primary['is_primary_loc'] ?? 'y',
                            'add1' => $primary['add1'] ?? '',
                            'add2' => $primary['add2'] ?? '',
                            'state_name' => $primary['state_name'] ?? 'Bangkok',
                            'city' => $primary['city'] ?? 'กรุงเทพมหานคร',
                            'zip' => $primary['zip'] ?? '10230',
                            'ccode' => $primary['ccode'] ?? 'TH',
                            'contact_pax' => $primary['contact_pax'] ?? '',
                            'email' => $primary['email'] ?? '',
                            'tel_work' => $primary['tel_work'] ?? '',
                            'tel_alt' => $primary['tel_alt'] ?? '',
                            'fax' => $primary['fax'] ?? '',
                        ];

                        // Debug the customer data
                        echo "<h3>Customer Data for Update Form:</h3>";
                        echo "<pre>";
                        print_r($customerForUpdate);
                        echo "</pre>";

                        // Redirect to the update tab
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("update-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'get':
                // Get a specific customer
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to get details.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                // Display the customer ID being searched
                echo "<h3>Searching for Customer ID:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            // Search specifically by the 'no' field (customer number)
                            'no' => $customerId
                        ]
                    ],
                    'Get Customer'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    // Check if we got any results
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer details retrieved successfully!';
                        $messageType = 'success';

                        // Fetch and display addresses for this customer
                        $customerDbId = $result['data']['items'][0]['id'];
                        $addressResult = makeApiRequest(
                            $client,
                            'GET',
                            $apiPath . '/' . $customerDbId . '/locations',
                            [
                                'headers' => [
                                    'X-api-key' => "{$apiKey}",
                                    'Accept' => 'application/json',
                                ]
                            ],
                            'Get Customer Address'
                        );
                        // Merge address data into customer data for display
                        if (isset($addressResult['data']['items'])) {
                            $result['data']['items'][0]['addresses'] = $addressResult['data']['items'];
                        }
                        // Show merged data in the response section
                        displayResults('Get Customer Response (JSON):', $result['data']['items'][0]);
                        return;
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                    }
                } else {
                    $message = 'Failed to get customer details. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_address':
                // Search for a customer to add address
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                echo "<h3>Searching for Customer to Add Address:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Address'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $message = 'Customer found! You can now add an address.';
                        $messageType = 'success';
                        $customerForAddress = $result['data']['items'][0];
                        // Store in session for next request
                        $_SESSION['customerForAddress'] = $customerForAddress;
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("add-address-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                        unset($_SESSION['customerForAddress']);
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                    unset($_SESSION['customerForAddress']);
                }
                break;

            case 'add_address':
                // Add address to selected customer
                // Always resolve numeric customerDbId from the latest customer search result
                $customerId = $_POST['customerId'] ?? '';
                $customerDbId = '';

                // Try to get numeric ID from session (from last search)
                if (isset($_SESSION['customerForAddress']['id']) && is_numeric($_SESSION['customerForAddress']['id'])) {
                    $customerDbId = $_SESSION['customerForAddress']['id'];
                } else {
                    // Fallback: search again by customer number to get numeric ID
                    $findResult = makeApiRequest(
                        $client,
                        'GET',
                        $apiPath,
                        [
                            'headers' => [
                                'X-api-key' => "{$apiKey}",
                                'Accept' => 'application/json',
                            ],
                            'query' => [
                                'no' => $customerId
                            ]
                        ],
                        'Find Customer by Number'
                    );
                    if (
                        $findResult['status'] === 200 &&
                        isset($findResult['data']['items'][0]['id'])
                    ) {
                        $customerDbId = $findResult['data']['items'][0]['id'];
                        // Update session for consistency
                        $_SESSION['customerForAddress'] = $findResult['data']['items'][0];
                    } else {
                        $message = 'Could not resolve numeric customer ID from customer number.';
                        $messageType = 'danger';
                        break;
                    }
                }

                // Debug: show which ID is being used and its type
                echo "<h3>POST Address to: {$apiPath}/{$customerDbId}/locations</h3>";
                echo "<pre>customerDbId: {$customerDbId} (type: " . gettype($customerDbId) . ")</pre>";

                $addressData = [
                    'loc_code' => $_POST['loc_code'] ?? '',
                    'loc_name' => $_POST['loc_name'] ?? '',
                    'add1' => $_POST['add1'] ?? '',
                    'ccode' => $_POST['ccode'] ?? '',
                    'state_code' => $_POST['state_code'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'status' => $_POST['status'] ?? 'a',
                    'is_primary_loc' => 'n',
                    'zip' => $_POST['zip'] ?? '',
                    'parent_id'=> 0,
                ];

                $result = makeApiRequest(
                    $client,
                    'POST',
                    $apiPath . '/' . $customerDbId . '/locations',
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $addressData
                    ],
                    'Add Address'
                );

                // After successful add, clear session
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Address added successfully!';
                    $messageType = 'success';
                    unset($_SESSION['customerForAddress']);
                } else {
                    $message = 'Failed to add address. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_update_location':
                // Search for a customer and list locations for update
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }
                $customerId = $_POST['customerId'];
                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for Update Location'
                );
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $customerForUpdateLocation = $result['data']['items'][0];
                        $_SESSION['customerForUpdateLocation'] = $customerForUpdateLocation;
                        // Get locations for this customer
                        $customerDbId = $customerForUpdateLocation['id'];
                        $locResult = makeApiRequest(
                            $client,
                            'GET',
                            $apiPath . '/' . $customerDbId . '/locations',
                            [
                                'headers' => [
                                    'X-api-key' => "{$apiKey}",
                                    'Accept' => 'application/json',
                                ]
                            ],
                            'Get Locations for Customer'
                        );
                        if ($locResult['status'] >= 200 && $locResult['status'] < 300 && isset($locResult['data']['items'])) {
                            $_SESSION['locationsForUpdate'] = $locResult['data']['items'];
                            $message = 'Customer and locations found! Select a location to update.';
                            $messageType = 'success';
                        } else {
                            $message = 'No locations found for this customer.';
                            $messageType = 'warning';
                            unset($_SESSION['locationsForUpdate']);
                        }
                        // Switch to update-location tab
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("update-location-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                        unset($_SESSION['customerForUpdateLocation'], $_SESSION['locationsForUpdate']);
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                    unset($_SESSION['customerForUpdateLocation'], $_SESSION['locationsForUpdate']);
                }
                break;

            case 'select_location_for_update':
                // Store selected location in session for update form
                if (!empty($_POST['locationId']) && isset($_SESSION['locationsForUpdate'])) {
                    foreach ($_SESSION['locationsForUpdate'] as $loc) {
                        if ($loc['id'] == $_POST['locationId']) {
                            $_SESSION['locationForUpdate'] = $loc;
                            break;
                        }
                    }
                    // Switch to update-location tab
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            document.getElementById("update-location-tab").click();
                        });
                    </script>';
                }
                break;

            case 'update_address':
                // Update address for selected location
                $customerDbId = $_POST['customerDbId'] ?? '';
                $locationId = $_POST['locationId'] ?? '';
                if (!$customerDbId || !$locationId) {
                    $message = 'Customer and Location ID are required.';
                    $messageType = 'danger';
                    break;
                }
                $addressData = [
                    'loc_code' => $_POST['loc_code'] ?? '',
                    'loc_name' => $_POST['loc_name'] ?? '',
                    'add1' => $_POST['add1'] ?? '',
                    'ccode' => $_POST['ccode'] ?? '',
                    'state_code' => $_POST['state_code'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'status' => $_POST['status'] ?? 'a',
                    'is_primary_loc' => $_POST['is_primary_loc'] ?? 'n',
                    'zip' => $_POST['zip'] ?? '',
                    'parent_id'=> 0,
                ];
                $result = makeApiRequest(
                    $client,
                    'PUT',
                    $apiPath . '/' . $customerDbId . '/locations/' . $locationId,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $addressData
                    ],
                    'Update Address'
                );
                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Address updated successfully!';
                    $messageType = 'success';
                    unset($_SESSION['locationForUpdate']);
                } else {
                    $message = 'Failed to update address. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_contact':
                // Search for a customer to add contact
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                // First get customer details from Appika API
                $getResult = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Get Customer for Contact'
                );

                if ($getResult['status'] >= 200 && $getResult['status'] < 300 &&
                    isset($getResult['data']['items']) && !empty($getResult['data']['items'])) {

                    $customerForContact = $getResult['data']['items'][0];
                    $_SESSION['customerForContact'] = $customerForContact;
                    $message = 'Customer found! You can now create a contact.';
                    $messageType = 'success';

                    // Switch to contact creation tab
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            document.getElementById("add-contact-tab").click();
                        });
                    </script>';
                } else {
                    $message = 'Failed to get customer details.';
                    $messageType = 'danger';
                    unset($_SESSION['customerForContact']);
                }
                break;

            case 'create_contact':
                // Always resolve numeric customerDbId from the latest customer search result
                $customerId = $_POST['customerId'] ?? '';
                $customerDbId = '';

                // Try to get numeric ID from session (from last search)
                if (isset($_SESSION['customerForContact']['id']) && is_numeric($_SESSION['customerForContact']['id'])) {
                    $customerDbId = $_SESSION['customerForContact']['id'];
                } else {
                    // Fallback: search again by customer number to get numeric ID
                    $findResult = makeApiRequest(
                        $client,
                        'GET',
                        $apiPath,
                        [
                            'headers' => [
                                'X-api-key' => "{$apiKey}",
                                'Accept' => 'application/json',
                            ],
                            'query' => [
                                'no' => $customerId
                            ]
                        ],
                        'Find Customer by Number'
                    );
                    if ($findResult['status'] === 200 && isset($findResult['data']['items'][0]['id'])) {
                        $customerDbId = $findResult['data']['items'][0]['id'];
                        $_SESSION['customerForContact'] = $findResult['data']['items'][0];
                    } else {
                        $message = 'Could not resolve numeric customer ID from customer number.';
                        $messageType = 'danger';
                        break;
                    }
                }

                // Debug: show which ID is being used and its type
                echo "<h3>POST Contact to: {$apiPath}/{$customerDbId}/contacts</h3>";
                echo "<pre>customerDbId: {$customerDbId} (type: " . gettype($customerDbId) . ")</pre>";

                $contactData = [
                    'first_name' => $_POST['first_name'] ?? '',
                    'last_name' => $_POST['last_name'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'tel_work' => $_POST['tel_work'] ?? '',
                    'tel_mobile' => $_POST['tel_mobile'] ?? '',
                    'status' => $_POST['status'] ?? 'a',
                    'is_primary' => $_POST['is_primary'] ?? 'n',
                    'grp_id' => $_POST['grp_id'] ?? '1',
                    'ofc_id' => '511',
                    'assign2' => '1',
                    'creator' => '1',
                    // Updated address fields to match Appika format
                    'add1' => $_POST['add1'] ?? '',
                    'add2' => $_POST['add2'] ?? '',
                    'city' => $_POST['city'] ?? '',
                    'ccode' => $_POST['ccode'] ?? '',
                    'cstate_code' => $_POST['cstate_code'] ?? '',
                    'cstate_name' => $_POST['cstate_name'] ?? '',
                    'zip' => $_POST['zip'] ?? '',
                ];

                $result = makeApiRequest(
                    $client,
                    'POST',
                    $apiPath . '/' . $customerDbId . '/contacts',
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                        ],
                        'json' => $contactData
                    ],
                    'Create Contact'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    // Get the new contact ID from response
                    $newContactId = $result['data']['id'] ?? '';
                    
                    if ($newContactId) {
                        // Get appika_id from customer data
                        $apikaId = $_SESSION['customerForContact']['no'] ?? '';

                        // Connect to database using existing configuration
                        require_once __DIR__ . '/../functions/server.php';

                        if (!$conn) {
                            $message = 'Database connection failed: Unable to connect to database';
                            $messageType = 'danger';
                        } else {
                            // Check if customer_contacts table exists, if not create it
                            $createTableQuery = "CREATE TABLE IF NOT EXISTS customer_contacts (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                customer_id VARCHAR(255) NOT NULL,
                                appika_id VARCHAR(255) NOT NULL,
                                contact_id VARCHAR(255) NOT NULL,
                                contact_no VARCHAR(255),
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX idx_customer_id (customer_id),
                                INDEX idx_appika_id (appika_id),
                                INDEX idx_contact_id (contact_id)
                            )";

                            if (!mysqli_query($conn, $createTableQuery)) {
                                error_log("Failed to create customer_contacts table: " . mysqli_error($conn));
                            }

                            // Insert contact record
                            $stmt = $conn->prepare("INSERT INTO customer_contacts (customer_id, appika_id, contact_id, contact_no) VALUES (?, ?, ?, ?)");
                            if ($stmt) {
                                $contactNo = $_POST['contactNo'] ?? '';
                                $stmt->bind_param("ssss", $customerId, $apikaId, $newContactId, $contactNo);

                                if ($stmt->execute()) {
                                    $message = 'Contact created and saved successfully!';
                                    $messageType = 'success';

                                    // Debug output
                                    echo "<pre>Saved to database:
Customer ID: {$customerId}
Appika ID: {$apikaId}
Contact ID: {$newContactId}
Contact No: {$contactNo}</pre>";
                                } else {
                                    $message = 'Contact created but failed to save to database: ' . $stmt->error;
                                    $messageType = 'warning';
                                }

                                $stmt->close();
                            } else {
                                $message = 'Contact created but failed to prepare database statement: ' . $conn->error;
                                $messageType = 'warning';
                            }
                        }
                    }
                    
                    unset($_SESSION['customerForContact']);
                } else {
                    $message = 'Failed to create contact. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_for_view_contacts':
                // Search for a customer to view contacts
                if (empty($_POST['customerId'])) {
                    $message = 'Customer ID is required to search.';
                    $messageType = 'danger';
                    break;
                }

                $customerId = $_POST['customerId'];

                echo "<h3>Searching for Customer to View Contacts:</h3>";
                echo "<pre>{$customerId}</pre>";

                $result = makeApiRequest(
                    $client,
                    'GET',
                    $apiPath,
                    [
                        'headers' => [
                            'X-api-key' => "{$apiKey}",
                            'Accept' => 'application/json',
                        ],
                        'query' => [
                            'no' => $customerId
                        ]
                    ],
                    'Search Customer for View Contacts'
                );

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    if (isset($result['data']['items']) && !empty($result['data']['items'])) {
                        $customerForViewContacts = $result['data']['items'][0];
                        $_SESSION['customerForViewContacts'] = $customerForViewContacts;

                        // Get contacts for this customer
                        $customerDbId = $customerForViewContacts['id'];
                        $contactsResult = makeApiRequest(
                            $client,
                            'GET',
                            $apiPath . '/' . $customerDbId . '/contacts',
                            [
                                'headers' => [
                                    'X-api-key' => "{$apiKey}",
                                    'Accept' => 'application/json',
                                ]
                            ],
                            'Get Customer Contacts'
                        );

                        if ($contactsResult['status'] >= 200 && $contactsResult['status'] < 300) {
                            if (isset($contactsResult['data']['items']) && !empty($contactsResult['data']['items'])) {
                                $_SESSION['customerContacts'] = $contactsResult['data']['items'];
                                $message = 'Customer found! Found ' . count($contactsResult['data']['items']) . ' contact(s).';
                                $messageType = 'success';
                            } else {
                                $_SESSION['customerContacts'] = [];
                                $message = 'Customer found but no contacts exist for this customer.';
                                $messageType = 'info';
                            }
                        } else {
                            $message = 'Customer found but failed to retrieve contacts.';
                            $messageType = 'warning';
                            unset($_SESSION['customerContacts']);
                        }

                        // Switch to view contacts tab
                        echo '<script>
                            document.addEventListener("DOMContentLoaded", function() {
                                document.getElementById("view-contacts-tab").click();
                            });
                        </script>';
                    } else {
                        $message = 'No customer found with the specified customer number.';
                        $messageType = 'warning';
                        unset($_SESSION['customerForViewContacts'], $_SESSION['customerContacts']);
                    }
                } else {
                    $message = 'Failed to search for customer. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                    unset($_SESSION['customerForViewContacts'], $_SESSION['customerContacts']);
                }
                break;

        }
    }
}

// Always use session value for customerForAddress if available
if (isset($_SESSION['customerForAddress'])) {
    $customerForAddress = $_SESSION['customerForAddress'];
}

// Always use session value for customerForUpdateLocation and locationsForUpdate if available
if (isset($_SESSION['customerForUpdateLocation'])) {
    $customerForUpdateLocation = $_SESSION['customerForUpdateLocation'];
}
if (isset($_SESSION['locationsForUpdate'])) {
    $locationsForUpdate = $_SESSION['locationsForUpdate'];
}
if (isset($_SESSION['locationForUpdate'])) {
    $locationForUpdate = $_SESSION['locationForUpdate'];
}
if (isset($_SESSION['customerForContact'])) {
    $customerForContact = $_SESSION['customerForContact'];
}
if (isset($_SESSION['customerForViewContacts'])) {
    $customerForViewContacts = $_SESSION['customerForViewContacts'];
}
if (isset($_SESSION['customerContacts'])) {
    $customerContacts = $_SESSION['customerContacts'];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Core API POST & PUT Example</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .card {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .btn-action {
        margin-right: 10px;
    }

    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }

    .nav-tabs {
        margin-bottom: 20px;
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
    }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Core API POST & PUT Examples</h1>
        <!-- Show token validation message -->
        <?php if (!empty($tokenMessage)): ?>
        <div class="alert alert-<?php echo $tokenMessageType; ?>" role="alert">
            <?php echo $tokenMessage; ?>
        </div>
        <?php endif; ?>
        <!-- Add API Token Input Form -->
        <form method="post" action="" class="mb-4">
            <div class="form-group">
                <label for="api_token"><strong>API Token</strong></label>
                <input type="text" class="form-control" id="api_token" name="api_token"
                    value="<?php echo htmlspecialchars($apiKey); ?>" required>
                <small class="form-text text-muted">Paste your API token here. This will be used for all API requests
                    below.</small>
            </div>
            <button type="submit" class="btn btn-secondary mb-2">Set Token</button>
        </form>

        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>" role="alert">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="apiTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="create-tab" data-toggle="tab" href="#create" role="tab">Create
                            (POST)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="update-tab" data-toggle="tab" href="#update" role="tab">Update (PUT)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="get-tab" data-toggle="tab" href="#get" role="tab">Get (GET)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="add-address-tab" data-toggle="tab" href="#add-address" role="tab">Add
                            Address (POST)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="update-location-tab" data-toggle="tab" href="#update-location"
                            role="tab">Update Address (PUT)</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="add-contact-tab" data-toggle="tab" href="#add-contact" role="tab">Create
                            Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="view-contacts-tab" data-toggle="tab" href="#view-contacts" role="tab">View
                            Contacts</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="apiTabsContent">
                    <!-- Create Customer Form (POST) -->
                    <div class="tab-pane fade show active" id="create" role="tabpanel">
                        <h3>Create New Customer</h3>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="create">

                            <div class="form-group">
                                <label for="customerNo">Customer No *</label>
                                <input type="text" class="form-control" id="customerNo" name="customerNo"
                                    placeholder="CUST1234" required value="CUST<?php echo rand(1000, 9999); ?>">
                                <small class="form-text text-muted">Unique customer identifier</small>
                            </div>

                            <div class="form-group">
                                <label for="customerName">Customer Name *</label>
                                <input type="text" class="form-control" id="customerName" name="customerName" required>
                            </div>

                            <div class="form-group">
                                <label for="customerEntityType">Entity Type *</label>
                                <select class="form-control" id="customerEntityType" name="customerEntityType" required>
                                    <option value="COMPANY">Company</option>
                                    <option value="INDIVIDUAL">Individual</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="customerGroup">Customer Group *</label>
                                <input type="text" class="form-control" id="customerGroup" name="customerGroup"
                                    value="1" required>
                            </div>

                            <div class="form-group">
                                <label for="customerStartDate">Start Date *</label>
                                <input type="date" class="form-control" id="customerStartDate" name="customerStartDate"
                                    value="<?php echo date('Y-m-d'); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status" value="a">
                                <small class="form-text text-muted">a = active</small>
                            </div>

                            <button type="submit" class="btn btn-primary">Create Customer</button>
                        </form>
                    </div>

                    <!-- Update Customer Form (PUT) -->
                    <div class="tab-pane fade" id="update" role="tabpanel">
                        <h3>Update Existing Customer</h3>

                        <!-- Customer Search Form -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForm">
                                    <input type="hidden" name="action" value="search_for_update">

                                    <div class="form-group">
                                        <label for="searchCustomerId">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerId" name="customerId"
                                            placeholder="Enter customer number (e.g., HI003)" required>
                                    </div>

                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>

                        <?php
                        // Display customer data if available from search
                        if (isset($customerForUpdate) && !empty($customerForUpdate)) {
                            $updateCustomerId = $customerForUpdate['no'] ?? '';
                            $updateCustomerName = $customerForUpdate['name'] ?? '';
                            $updateEntityType = $customerForUpdate['entity_type'] == '1' ? 'COMPANY' : 'INDIVIDUAL';
                            $updateCustomerGroup = $customerForUpdate['grp_id'] ?? '1';
                            $updateStartDate = $customerForUpdate['start_date'] ?? date('Y-m-d');
                            $updateStatus = $customerForUpdate['status'] ?? 'a';
                        } else {
                            $updateCustomerId = '';
                            $updateCustomerName = '';
                            $updateEntityType = 'COMPANY';
                            $updateCustomerGroup = '1';
                            $updateStartDate = date('Y-m-d');
                            $updateStatus = 'a';
                        }
                        ?>

                        <!-- Update Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update">

                            <div class="form-group">
                                <label for="updateCustomerId">Customer No *</label>
                                <input type="text" class="form-control" id="updateCustomerId" name="customerId" required
                                    value="<?php echo htmlspecialchars($updateCustomerId); ?>"
                                    <?php echo !empty($updateCustomerId) ? 'readonly' : ''; ?>>
                                <small class="form-text text-muted">Customer number cannot be changed</small>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerName">Customer Name *</label>
                                <input type="text" class="form-control" id="updateCustomerName" name="customerName"
                                    value="<?php echo htmlspecialchars($updateCustomerName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerEntityType">Entity Type *</label>
                                <select class="form-control" id="updateCustomerEntityType" name="customerEntityType"
                                    required>
                                    <option value="COMPANY"
                                        <?php echo $updateEntityType == 'COMPANY' ? 'selected' : ''; ?>>Company</option>
                                    <option value="INDIVIDUAL"
                                        <?php echo $updateEntityType == 'INDIVIDUAL' ? 'selected' : ''; ?>>Individual
                                    </option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerGroup">Customer Group *</label>
                                <input type="text" class="form-control" id="updateCustomerGroup" name="customerGroup"
                                    value="<?php echo htmlspecialchars($updateCustomerGroup); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="updateCustomerStartDate">Start Date *</label>
                                <input type="date" class="form-control" id="updateCustomerStartDate"
                                    name="customerStartDate" value="<?php echo htmlspecialchars($updateStartDate); ?>"
                                    required>
                            </div>

                            <div class="form-group">
                                <label for="updateStatus">Status</label>
                                <input type="text" class="form-control" id="updateStatus" name="status"
                                    value="<?php echo htmlspecialchars($updateStatus); ?>">
                                <small class="form-text text-muted">a = active</small>
                            </div>

                            <button type="submit" class="btn btn-warning"
                                <?php echo empty($updateCustomerId) ? 'disabled' : ''; ?>>
                                <?php echo empty($updateCustomerId) ? 'Search for a Customer First' : 'Update Customer'; ?>
                            </button>
                        </form>
                    </div>



                    <!-- Get Customer Form (GET) -->
                    <div class="tab-pane fade" id="get" role="tabpanel">
                        <h3>Get Customer Details</h3>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get">

                            <div class="form-group">
                                <label for="getCustomerId">Customer ID *</label>
                                <input type="text" class="form-control" id="getCustomerId" name="customerId" required
                                    value="<?php echo htmlspecialchars($customerId); ?>">
                            </div>

                            <button type="submit" class="btn btn-info">Get Customer</button>
                        </form>
                    </div>

                    <!-- Add Address Tab -->
                    <div class="tab-pane fade" id="add-address" role="tabpanel">
                        <h3>Add Address to Customer</h3>
                        <!-- Search Customer for Address -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForAddressForm">
                                    <input type="hidden" name="action" value="search_for_address">
                                    <div class="form-group">
                                        <label for="searchCustomerIdForAddress">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerIdForAddress"
                                            name="customerId" placeholder="Enter customer number (e.g., HI003)"
                                            required>
                                    </div>
                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>
                        <?php
                        if (isset($customerForAddress) && !empty($customerForAddress)) {
                            $addressCustomerId = $customerForAddress['no'] ?? '';
                            $addressCustomerDbId = $customerForAddress['id'] ?? '';
                        ?>
                        <!-- Show Customer Number -->
                        <div class="alert alert-secondary">
                            <strong>Customer Number:</strong> <?php echo htmlspecialchars($addressCustomerId); ?>
                        </div>
                        <!-- Address Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="add_address">
                            <input type="hidden" name="customerId"
                                value="<?php echo htmlspecialchars($addressCustomerId); ?>">
                            <input type="hidden" name="customerDbId"
                                value="<?php echo htmlspecialchars($addressCustomerDbId); ?>">

                            <div class="form-group">
                                <label for="loc_code">Location Code *</label>
                                <input type="text" class="form-control" id="loc_code" name="loc_code" required
                                    value="LOC<?php echo rand(1000,9999); ?>">
                            </div>
                            <div class="form-group">
                                <label for="loc_name">Location Name *</label>
                                <input type="text" class="form-control" id="loc_name" name="loc_name" required>
                            </div>
                            <div class="form-group">
                                <label for="add1">Address *</label>
                                <input type="text" class="form-control" id="add1" name="add1" required>
                            </div>
                            <div class="form-group">
                                <label for="ccode">Country Code *</label>
                                <input type="text" class="form-control" id="ccode" name="ccode" value="TH" required>
                            </div>
                            <div class="form-group">
                                <label for="state_code">State Code *</label>
                                <input type="text" class="form-control" id="state_code" name="state_code" value="10"
                                    required>
                            </div>
                            <div class="form-group">
                                <label for="city">City *</label>
                                <input type="text" class="form-control" id="city" name="city" value="Bangkok" required>
                            </div>
                            <div class="form-group">
                                <label for="zip">ZIP *</label>
                                <input type="text" class="form-control" id="zip" name="zip" value="10230" required>
                            </div>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status" value="a">
                            </div>
                            <div class="form-group">
                                <label for="is_primary_loc">Is Primary Location</label>
                                <input type="text" class="form-control" id="is_primary_loc" name="is_primary_loc"
                                    value="n" readonly>
                                <small class="form-text text-muted">Always "n" for this form</small>
                            </div>
                            <button type="submit" class="btn btn-success">Add Address</button>
                        </form>
                        <?php } ?>
                    </div>
                    <!-- Update Address Tab -->
                    <div class="tab-pane fade" id="update-location" role="tabpanel">
                        <h3>Update Address for Customer</h3>
                        <!-- Search Customer for Address Update -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForUpdateLocationForm">
                                    <input type="hidden" name="action" value="search_for_update_location">
                                    <div class="form-group">
                                        <label for="searchCustomerIdForUpdateLocation">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerIdForUpdateLocation"
                                            name="customerId" placeholder="Enter customer number (e.g., HI003)"
                                            required>
                                    </div>
                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>
                        <?php
                        // Only show locations dropdown if a customer has been found and locations are available
                        if (
                            isset($customerForUpdateLocation) && !empty($customerForUpdateLocation)
                            && isset($locationsForUpdate) && !empty($locationsForUpdate)
                        ) {
                        ?>
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Select Location to Update</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="">
                                    <input type="hidden" name="action" value="select_location_for_update">
                                    <div class="form-group">
                                        <label for="locationId">Location *</label>
                                        <select class="form-control" id="locationId" name="locationId" required>
                                            <option value="">-- Select Location --</option>
                                            <?php foreach ($locationsForUpdate as $loc): ?>
                                            <option value="<?php echo htmlspecialchars($loc['id']); ?>"
                                                <?php if (isset($locationForUpdate) && $locationForUpdate['id'] == $loc['id']) echo 'selected'; ?>>
                                                <?php echo htmlspecialchars($loc['loc_code'] . ' - ' . $loc['loc_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Edit Location</button>
                                </form>
                            </div>
                        </div>
                        <?php } ?>

                        <?php
                        // If a location is selected for update, show the update form
                        if (isset($locationForUpdate) && !empty($locationForUpdate) && isset($customerForUpdateLocation)) {
                        ?>
                        <!-- Show Customer Number -->
                        <div class="alert alert-secondary">
                            <strong>Customer Number:</strong>
                            <?php echo htmlspecialchars($customerForUpdateLocation['no'] ?? ''); ?>
                        </div>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_address">
                            <input type="hidden" name="customerDbId"
                                value="<?php echo htmlspecialchars($customerForUpdateLocation['id']); ?>">
                            <input type="hidden" name="locationId"
                                value="<?php echo htmlspecialchars($locationForUpdate['id']); ?>">

                            <div class="form-group">
                                <label for="loc_code">Location Code *</label>
                                <input type="text" class="form-control" id="loc_code" name="loc_code" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['loc_code'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="loc_name">Location Name *</label>
                                <input type="text" class="form-control" id="loc_name" name="loc_name" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['loc_name'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="add1">Address *</label>
                                <input type="text" class="form-control" id="add1" name="add1" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['add1'] ?? ''); ?>">
                            </div>
                            <div class="form-group">
                                <label for="ccode">Country Code *</label>
                                <input type="text" class="form-control" id="ccode" name="ccode" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['ccode'] ?? 'TH'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="state_code">State Code *</label>
                                <input type="text" class="form-control" id="state_code" name="state_code" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['state_code'] ?? '10'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="city">City *</label>
                                <input type="text" class="form-control" id="city" name="city" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['city'] ?? 'Bangkok'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="zip">ZIP *</label>
                                <input type="text" class="form-control" id="zip" name="zip" required
                                    value="<?php echo htmlspecialchars($locationForUpdate['zip'] ?? '10230'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status"
                                    value="<?php echo htmlspecialchars($locationForUpdate['status'] ?? 'a'); ?>">
                            </div>
                            <div class="form-group">
                                <label for="is_primary_loc">Is Primary Location</label>
                                <input type="text" class="form-control" id="is_primary_loc" name="is_primary_loc"
                                    value="<?php echo htmlspecialchars($locationForUpdate['is_primary_loc'] ?? 'n'); ?>">
                            </div>
                            <button type="submit" class="btn btn-warning">Update Address</button>
                        </form>
                        <?php } ?>
                    </div>

                    <!-- Create Contact Tab -->
                    <div class="tab-pane fade" id="add-contact" role="tabpanel">
                        <h3>Add Contact to Customer</h3>
                        <!-- Search Customer for Contact -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="searchCustomerForContactForm">
                                    <input type="hidden" name="action" value="search_for_contact">
                                    <div class="form-group">
                                        <label for="searchCustomerIdForContact">Customer Number *</label>
                                        <input type="text" class="form-control" id="searchCustomerIdForContact"
                                            name="customerId" placeholder="Enter customer number (e.g., HI003)"
                                            required>
                                    </div>
                                    <button type="submit" class="btn btn-info">Search Customer</button>
                                </form>
                            </div>
                        </div>
                        <?php
                        if (isset($customerForContact) && !empty($customerForContact)) {
                            $contactCustomerId = $customerForContact['no'] ?? '';
                            $contactCustomerDbId = $customerForContact['id'] ?? '';
                        ?>
                        <!-- Show Customer Info -->
                        <div class="alert alert-secondary">
                            <strong>Customer Number:</strong> <?php echo htmlspecialchars($contactCustomerId); ?><br>
                            <strong>Customer Name:</strong>
                            <?php echo htmlspecialchars($customerForContact['name'] ?? ''); ?>
                        </div>
                        <!-- Contact Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="create_contact">
                            <input type="hidden" name="customerId" value="<?php echo htmlspecialchars($contactCustomerId); ?>">
                            <input type="hidden" name="customerDbId" value="<?php echo htmlspecialchars($contactCustomerDbId); ?>">

                            <?php
                            // Get customer details to pre-fill contact form
                            $customerName = $customerForContact['name'] ?? '';
                            // Split customer name into first and last name
                            $nameParts = explode(' ', $customerName, 2);
                            $firstName = $nameParts[0] ?? '';
                            $lastName = $nameParts[1] ?? '';
                            $customerEmail = $customerForContact['primary_location']['email'] ?? '';
                            $customerPhone = $customerForContact['primary_location']['tel_work'] ?? '';
                            $customerMobile = $customerForContact['primary_location']['tel_alt'] ?? '';
                            ?>

                            <div class="form-group">
                                <label for="first_name">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                    value="<?php echo htmlspecialchars($firstName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="last_name">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                    value="<?php echo htmlspecialchars($lastName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                    value="<?php echo htmlspecialchars($customerEmail); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="tel_work">Work Phone</label>
                                <input type="text" class="form-control" id="tel_work" name="tel_work" 
                                    value="<?php echo htmlspecialchars($customerPhone); ?>">
                            </div>

                            <div class="form-group">
                                <label for="tel_mobile">Mobile Phone</label>
                                <input type="text" class="form-control" id="tel_mobile" name="tel_mobile"
                                    value="<?php echo htmlspecialchars($customerMobile); ?>">
                            </div>

                            <div class="form-group">
                                <label for="contactNo">Contact Number (Optional)</label>
                                <input type="text" class="form-control" id="contactNo" name="contactNo"
                                    placeholder="e.g., CONT001" value="CONT<?php echo rand(1000, 9999); ?>">
                                <small class="form-text text-muted">Internal reference number for this contact</small>
                            </div>

                            <div class="form-group">
                                <label for="grp_id">Group ID *</label>
                                <input type="text" class="form-control" id="grp_id" name="grp_id" 
                                    value="<?php echo htmlspecialchars($customerForContact['grp_id'] ?? '1'); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="is_primary">Is Primary Contact</label>
                                <input type="text" class="form-control" id="is_primary" name="is_primary" value="n">
                                <small class="form-text text-muted">y = yes, n = no</small>
                            </div>

                            <div class="form-group">
                                <label for="status">Status</label>
                                <input type="text" class="form-control" id="status" name="status" 
                                    value="<?php echo htmlspecialchars($customerForContact['status'] ?? 'a'); ?>">
                                <small class="form-text text-muted">a = active</small>
                            </div>

                            <!-- Updated address fields in form -->
                            <div class="form-group">
                                <label for="add1">Address Line 1</label>
                                <input type="text" class="form-control" id="add1" name="add1" 
                                    placeholder="e.g., 410 Terry Ave N">
                            </div>

                            <div class="form-group">
                                <label for="add2">Address Line 2</label>
                                <input type="text" class="form-control" id="add2" name="add2"
                                    placeholder="e.g., 555 Test">
                            </div>

                            <div class="form-group">
                                <label for="city">City</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                    placeholder="e.g., Seattle">
                            </div>

                            <div class="form-group">
                                <label for="ccode">Country *</label>
                                <select class="form-control" id="ccode" name="ccode" required onchange="loadStates(this.value)">
                                    <option value="">Select Country</option>
                                    <?php
                                    try {
                                        $countries = getCountriesMapping();
                                        foreach ($countries as $country) {
                                            echo '<option value="' . htmlspecialchars($country['code']) . '">' .
                                                 htmlspecialchars($country['name']) . '</option>';
                                        }
                                    } catch (Exception $e) {
                                        echo '<option value="">Error loading countries</option>';
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="cstate_name">State/Province *</label>
                                <select class="form-control" id="cstate_name" name="cstate_name" required onchange="updateStateCode()">
                                    <option value="">Select Country First</option>
                                </select>
                                <input type="hidden" id="cstate_code" name="cstate_code">
                            </div>

                            <div class="form-group">
                                <label for="zip">ZIP Code</label>
                                <input type="text" class="form-control" id="zip" name="zip"
                                    placeholder="e.g., 98109">
                            </div>

                            <button type="submit" class="btn btn-success">Create Contact</button>
                        </form>
                        <?php } ?>
                    </div>

                    <!-- View Customer Contacts -->
                    <div class="tab-pane fade" id="view-contacts" role="tabpanel">
                        <h3>View Customer Contacts</h3>

                        <!-- Customer Search Form -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Search for Customer</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="">
                                    <input type="hidden" name="action" value="search_for_view_contacts">

                                    <div class="form-group">
                                        <label for="viewContactsCustomerId">Customer Number *</label>
                                        <input type="text" class="form-control" id="viewContactsCustomerId" name="customerId"
                                            placeholder="Enter customer number (e.g., HI003)" required>
                                    </div>

                                    <button type="submit" class="btn btn-info">Search Customer & View Contacts</button>
                                </form>
                            </div>
                        </div>

                        <?php if (isset($customerForViewContacts) && !empty($customerForViewContacts)): ?>
                        <!-- Customer Info -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">Customer Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Customer Number:</strong> <?php echo htmlspecialchars($customerForViewContacts['no'] ?? ''); ?></p>
                                        <p><strong>Customer Name:</strong> <?php echo htmlspecialchars($customerForViewContacts['name'] ?? ''); ?></p>
                                        <p><strong>Entity Type:</strong> <?php echo ($customerForViewContacts['entity_type'] ?? '') == '1' ? 'Company' : 'Individual'; ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Status:</strong> <span class="badge badge-<?php echo ($customerForViewContacts['status'] ?? '') == 'a' ? 'success' : 'secondary'; ?>"><?php echo ($customerForViewContacts['status'] ?? '') == 'a' ? 'Active' : 'Inactive'; ?></span></p>
                                        <p><strong>Start Date:</strong> <?php echo htmlspecialchars($customerForViewContacts['start_date'] ?? ''); ?></p>
                                        <p><strong>Group ID:</strong> <?php echo htmlspecialchars($customerForViewContacts['grp_id'] ?? ''); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contacts List -->
                        <?php if (isset($customerContacts) && !empty($customerContacts)): ?>
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Customer Contacts (<?php echo count($customerContacts); ?> found)</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th>Contact ID</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Phone Numbers</th>
                                                <th>Address</th>
                                                <th>Country/State</th>
                                                <th>Group</th>
                                                <th>Status</th>
                                                <th>Primary</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($customerContacts as $contact): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($contact['id'] ?? ''); ?></strong>
                                                    <?php if (!empty($contact['contact_no'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($contact['contact_no']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $firstName = htmlspecialchars($contact['first_name'] ?? '');
                                                    $lastName = htmlspecialchars($contact['last_name'] ?? '');
                                                    $fullName = trim($firstName . ' ' . $lastName);
                                                    echo $fullName ?: '<span class="text-muted">No name</span>';
                                                    ?>
                                                    <?php if (!empty($contact['title'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($contact['title']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($contact['email'])): ?>
                                                        <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>" class="text-primary">
                                                            <i class="fa fa-envelope"></i> <?php echo htmlspecialchars($contact['email']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $phones = [];
                                                    if (!empty($contact['tel_work'])) {
                                                        $phones[] = '<i class="fa fa-phone"></i> <a href="tel:' . htmlspecialchars($contact['tel_work']) . '">' . htmlspecialchars($contact['tel_work']) . '</a> <small class="text-muted">(Work)</small>';
                                                    }
                                                    if (!empty($contact['tel_mobile'])) {
                                                        $phones[] = '<i class="fa fa-mobile-alt"></i> <a href="tel:' . htmlspecialchars($contact['tel_mobile']) . '">' . htmlspecialchars($contact['tel_mobile']) . '</a> <small class="text-muted">(Mobile)</small>';
                                                    }
                                                    if (!empty($contact['tel_home'])) {
                                                        $phones[] = '<i class="fa fa-home"></i> <a href="tel:' . htmlspecialchars($contact['tel_home']) . '">' . htmlspecialchars($contact['tel_home']) . '</a> <small class="text-muted">(Home)</small>';
                                                    }
                                                    if (!empty($contact['fax'])) {
                                                        $phones[] = '<i class="fa fa-fax"></i> ' . htmlspecialchars($contact['fax']) . ' <small class="text-muted">(Fax)</small>';
                                                    }

                                                    if (!empty($phones)) {
                                                        echo implode('<br>', $phones);
                                                    } else {
                                                        echo '<span class="text-muted">-</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $address = [];
                                                    if (!empty($contact['add1'])) $address[] = $contact['add1'];
                                                    if (!empty($contact['add2'])) $address[] = $contact['add2'];
                                                    if (!empty($contact['city'])) $address[] = $contact['city'];

                                                    if (!empty($address)) {
                                                        echo '<i class="fa fa-map-marker-alt text-muted"></i> ' . htmlspecialchars(implode(', ', $address));
                                                        if (!empty($contact['zip'])) {
                                                            echo '<br><small class="text-muted">ZIP: ' . htmlspecialchars($contact['zip']) . '</small>';
                                                        }
                                                    } else {
                                                        echo '<span class="text-muted">-</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $location = [];
                                                    if (!empty($contact['ccode'])) {
                                                        $location[] = '<i class="fa fa-flag"></i> ' . htmlspecialchars($contact['ccode']);
                                                    }
                                                    if (!empty($contact['cstate_name'])) {
                                                        $location[] = htmlspecialchars($contact['cstate_name']);
                                                    }
                                                    if (!empty($contact['cstate_code'])) {
                                                        $location[] = '<small class="text-muted">(' . htmlspecialchars($contact['cstate_code']) . ')</small>';
                                                    }

                                                    if (!empty($location)) {
                                                        echo implode('<br>', $location);
                                                    } else {
                                                        echo '<span class="text-muted">-</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($contact['grp_id'])): ?>
                                                        <span class="badge badge-info">Group <?php echo htmlspecialchars($contact['grp_id']); ?></span>
                                                        <?php if (!empty($contact['ofc_id'])): ?>
                                                            <br><small class="text-muted">Office: <?php echo htmlspecialchars($contact['ofc_id']); ?></small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo ($contact['status'] ?? '') == 'a' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ($contact['status'] ?? '') == 'a' ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (($contact['is_primary'] ?? '') == 'y'): ?>
                                                        <span class="badge badge-warning"><i class="fa fa-star"></i> Primary</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($contact['created_at'])): ?>
                                                        <small><?php echo date('M j, Y', strtotime($contact['created_at'])); ?></small>
                                                        <br><small class="text-muted"><?php echo date('H:i', strtotime($contact['created_at'])); ?></small>
                                                    <?php elseif (!empty($contact['date_created'])): ?>
                                                        <small><?php echo date('M j, Y', strtotime($contact['date_created'])); ?></small>
                                                        <br><small class="text-muted"><?php echo date('H:i', strtotime($contact['date_created'])); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <?php if (!empty($contact['email'])): ?>
                                                            <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>" class="btn btn-outline-primary btn-sm" title="Send Email">
                                                                <i class="fa fa-envelope"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (!empty($contact['tel_work']) || !empty($contact['tel_mobile'])): ?>
                                                            <a href="tel:<?php echo htmlspecialchars($contact['tel_work'] ?: $contact['tel_mobile']); ?>" class="btn btn-outline-success btn-sm" title="Call">
                                                                <i class="fa fa-phone"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <button class="btn btn-outline-info btn-sm" title="View Details" onclick="showContactDetails(<?php echo htmlspecialchars(json_encode($contact)); ?>)">
                                                            <i class="fa fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <h6><i class="fa fa-info-circle"></i> Contact Information:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Total Contacts:</strong> <?php echo count($customerContacts); ?></li>
                                        <li><strong>Active Contacts:</strong> <?php echo count(array_filter($customerContacts, function($c) { return ($c['status'] ?? '') == 'a'; })); ?></li>
                                        <li><strong>Primary Contacts:</strong> <?php echo count(array_filter($customerContacts, function($c) { return ($c['is_primary'] ?? '') == 'y'; })); ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">No Contacts Found</h5>
                            </div>
                            <div class="card-body">
                                <p>No contacts found for this customer.</p>
                                <p>You can create a new contact using the <strong>"Create Contact"</strong> tab.</p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
        </div>

        <!-- Contact Details Modal -->
        <div class="modal fade" id="contactDetailsModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Contact Details</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="contactDetailsBody">
                        <!-- Contact details will be populated here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="core_api_example.php" class="btn btn-secondary">View GET Examples</a>
            <a href="../merlion/admin-tickets.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
    function loadStates(countryCode) {
        const stateSelect = document.getElementById('cstate_name');
        const stateCodeInput = document.getElementById('cstate_code');

        // Clear current options
        stateSelect.innerHTML = '<option value="">Loading...</option>';
        stateCodeInput.value = '';

        if (!countryCode) {
            stateSelect.innerHTML = '<option value="">Select Country First</option>';
            return;
        }

        // Make AJAX request to get states
        fetch('../functions/get-states-api.php?country=' + encodeURIComponent(countryCode))
            .then(response => response.json())
            .then(data => {
                stateSelect.innerHTML = '<option value="">Select State/Province</option>';

                if (data.success && data.states) {
                    data.states.forEach(state => {
                        const option = document.createElement('option');
                        option.value = state.english_name;
                        option.textContent = state.local_name + (state.local_name !== state.english_name ? ' — ' + state.english_name : '');
                        option.setAttribute('data-code', state.code);
                        stateSelect.appendChild(option);
                    });
                } else {
                    stateSelect.innerHTML = '<option value="">No states available</option>';
                }
            })
            .catch(error => {
                console.error('Error loading states:', error);
                stateSelect.innerHTML = '<option value="">Error loading states</option>';
            });
    }

    function updateStateCode() {
        const stateSelect = document.getElementById('cstate_name');
        const stateCodeInput = document.getElementById('cstate_code');
        const selectedOption = stateSelect.options[stateSelect.selectedIndex];

        if (selectedOption && selectedOption.getAttribute('data-code')) {
            stateCodeInput.value = selectedOption.getAttribute('data-code');
        } else {
            stateCodeInput.value = '';
        }
    }

    function showContactDetails(contact) {
        const modalBody = document.getElementById('contactDetailsBody');

        let html = '<div class="row">';

        // Personal Information
        html += '<div class="col-md-6">';
        html += '<h6 class="text-primary"><i class="fa fa-user"></i> Personal Information</h6>';
        html += '<table class="table table-sm">';
        html += '<tr><td><strong>Contact ID:</strong></td><td>' + (contact.id || '-') + '</td></tr>';
        html += '<tr><td><strong>First Name:</strong></td><td>' + (contact.first_name || '-') + '</td></tr>';
        html += '<tr><td><strong>Last Name:</strong></td><td>' + (contact.last_name || '-') + '</td></tr>';
        html += '<tr><td><strong>Title:</strong></td><td>' + (contact.title || '-') + '</td></tr>';
        html += '<tr><td><strong>Email:</strong></td><td>' + (contact.email ? '<a href="mailto:' + contact.email + '">' + contact.email + '</a>' : '-') + '</td></tr>';
        html += '</table>';
        html += '</div>';

        // Contact Information
        html += '<div class="col-md-6">';
        html += '<h6 class="text-success"><i class="fa fa-phone"></i> Contact Information</h6>';
        html += '<table class="table table-sm">';
        html += '<tr><td><strong>Work Phone:</strong></td><td>' + (contact.tel_work ? '<a href="tel:' + contact.tel_work + '">' + contact.tel_work + '</a>' : '-') + '</td></tr>';
        html += '<tr><td><strong>Mobile Phone:</strong></td><td>' + (contact.tel_mobile ? '<a href="tel:' + contact.tel_mobile + '">' + contact.tel_mobile + '</a>' : '-') + '</td></tr>';
        html += '<tr><td><strong>Home Phone:</strong></td><td>' + (contact.tel_home ? '<a href="tel:' + contact.tel_home + '">' + contact.tel_home + '</a>' : '-') + '</td></tr>';
        html += '<tr><td><strong>Fax:</strong></td><td>' + (contact.fax || '-') + '</td></tr>';
        html += '<tr><td><strong>Website:</strong></td><td>' + (contact.website ? '<a href="' + contact.website + '" target="_blank">' + contact.website + '</a>' : '-') + '</td></tr>';
        html += '</table>';
        html += '</div>';

        html += '</div><div class="row">';

        // Address Information
        html += '<div class="col-md-6">';
        html += '<h6 class="text-info"><i class="fa fa-map-marker-alt"></i> Address Information</h6>';
        html += '<table class="table table-sm">';
        html += '<tr><td><strong>Address Line 1:</strong></td><td>' + (contact.add1 || '-') + '</td></tr>';
        html += '<tr><td><strong>Address Line 2:</strong></td><td>' + (contact.add2 || '-') + '</td></tr>';
        html += '<tr><td><strong>City:</strong></td><td>' + (contact.city || '-') + '</td></tr>';
        html += '<tr><td><strong>State/Province:</strong></td><td>' + (contact.cstate_name || '-') + '</td></tr>';
        html += '<tr><td><strong>State Code:</strong></td><td>' + (contact.cstate_code || '-') + '</td></tr>';
        html += '<tr><td><strong>ZIP Code:</strong></td><td>' + (contact.zip || '-') + '</td></tr>';
        html += '<tr><td><strong>Country:</strong></td><td>' + (contact.ccode || '-') + '</td></tr>';
        html += '</table>';
        html += '</div>';

        // System Information
        html += '<div class="col-md-6">';
        html += '<h6 class="text-warning"><i class="fa fa-cog"></i> System Information</h6>';
        html += '<table class="table table-sm">';
        html += '<tr><td><strong>Status:</strong></td><td><span class="badge badge-' + (contact.status === 'a' ? 'success' : 'secondary') + '">' + (contact.status === 'a' ? 'Active' : 'Inactive') + '</span></td></tr>';
        html += '<tr><td><strong>Primary Contact:</strong></td><td><span class="badge badge-' + (contact.is_primary === 'y' ? 'warning' : 'light') + '">' + (contact.is_primary === 'y' ? 'Yes' : 'No') + '</span></td></tr>';
        html += '<tr><td><strong>Group ID:</strong></td><td>' + (contact.grp_id || '-') + '</td></tr>';
        html += '<tr><td><strong>Office ID:</strong></td><td>' + (contact.ofc_id || '-') + '</td></tr>';
        html += '<tr><td><strong>Assigned To:</strong></td><td>' + (contact.assign2 || '-') + '</td></tr>';
        html += '<tr><td><strong>Creator:</strong></td><td>' + (contact.creator || '-') + '</td></tr>';
        html += '<tr><td><strong>Created:</strong></td><td>' + (contact.created_at ? new Date(contact.created_at).toLocaleString() : (contact.date_created ? new Date(contact.date_created).toLocaleString() : '-')) + '</td></tr>';
        html += '</table>';
        html += '</div>';

        html += '</div>';

        // Additional Notes
        if (contact.notes || contact.description || contact.remarks) {
            html += '<div class="row"><div class="col-12">';
            html += '<h6 class="text-secondary"><i class="fa fa-sticky-note"></i> Additional Information</h6>';
            html += '<div class="alert alert-light">';
            html += (contact.notes || contact.description || contact.remarks || 'No additional notes available.');
            html += '</div>';
            html += '</div></div>';
        }

        modalBody.innerHTML = html;
        $('#contactDetailsModal').modal('show');
    }
    </script>
</body>

</html>

<!-- Explanation for "Invalid parent id" error:
This error means the API endpoint /contact/customers/{id}/locations expects {id} to be the numeric internal customer ID (not the customer number).
If you use the customer number (e.g., "CUST1234") instead of the numeric ID (e.g., 123), the API will return "Invalid parent id".
Always use the numeric "id" field from the customer search result, not the "no" field, in the URL path for adding an address. -->