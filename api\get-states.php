<?php
/**
 * API endpoint to get states/provinces for a country
 * Uses the geo API for data mapping
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include the state mapping functions
require_once __DIR__ . '/../functions/state-mapping.php';

/**
 * Fallback states for common countries when geographer fails
 */
if (!function_exists('getFallbackStates')) {
function getFallbackStates($countryCode) {
    $fallbacks = [
        'US' => [ // United States
            ['value' => 'US-AL', 'text' => 'Alabama'],
            ['value' => 'US-AK', 'text' => 'Alaska'],
            ['value' => 'US-AZ', 'text' => 'Arizona'],
            ['value' => 'US-AR', 'text' => 'Arkansas'],
            ['value' => 'US-CA', 'text' => 'California'],
            ['value' => 'US-CO', 'text' => 'Colorado'],
            ['value' => 'US-CT', 'text' => 'Connecticut'],
            ['value' => 'US-DE', 'text' => 'Delaware'],
            ['value' => 'US-FL', 'text' => 'Florida'],
            ['value' => 'US-GA', 'text' => 'Georgia'],
            ['value' => 'US-HI', 'text' => 'Hawaii'],
            ['value' => 'US-ID', 'text' => 'Idaho'],
            ['value' => 'US-IL', 'text' => 'Illinois'],
            ['value' => 'US-IN', 'text' => 'Indiana'],
            ['value' => 'US-IA', 'text' => 'Iowa'],
            ['value' => 'US-KS', 'text' => 'Kansas'],
            ['value' => 'US-KY', 'text' => 'Kentucky'],
            ['value' => 'US-LA', 'text' => 'Louisiana'],
            ['value' => 'US-ME', 'text' => 'Maine'],
            ['value' => 'US-MD', 'text' => 'Maryland'],
            ['value' => 'US-MA', 'text' => 'Massachusetts'],
            ['value' => 'US-MI', 'text' => 'Michigan'],
            ['value' => 'US-MN', 'text' => 'Minnesota'],
            ['value' => 'US-MS', 'text' => 'Mississippi'],
            ['value' => 'US-MO', 'text' => 'Missouri'],
            ['value' => 'US-MT', 'text' => 'Montana'],
            ['value' => 'US-NE', 'text' => 'Nebraska'],
            ['value' => 'US-NV', 'text' => 'Nevada'],
            ['value' => 'US-NH', 'text' => 'New Hampshire'],
            ['value' => 'US-NJ', 'text' => 'New Jersey'],
            ['value' => 'US-NM', 'text' => 'New Mexico'],
            ['value' => 'US-NY', 'text' => 'New York'],
            ['value' => 'US-NC', 'text' => 'North Carolina'],
            ['value' => 'US-ND', 'text' => 'North Dakota'],
            ['value' => 'US-OH', 'text' => 'Ohio'],
            ['value' => 'US-OK', 'text' => 'Oklahoma'],
            ['value' => 'US-OR', 'text' => 'Oregon'],
            ['value' => 'US-PA', 'text' => 'Pennsylvania'],
            ['value' => 'US-RI', 'text' => 'Rhode Island'],
            ['value' => 'US-SC', 'text' => 'South Carolina'],
            ['value' => 'US-SD', 'text' => 'South Dakota'],
            ['value' => 'US-TN', 'text' => 'Tennessee'],
            ['value' => 'US-TX', 'text' => 'Texas'],
            ['value' => 'US-UT', 'text' => 'Utah'],
            ['value' => 'US-VT', 'text' => 'Vermont'],
            ['value' => 'US-VA', 'text' => 'Virginia'],
            ['value' => 'US-WA', 'text' => 'Washington'],
            ['value' => 'US-WV', 'text' => 'West Virginia'],
            ['value' => 'US-WI', 'text' => 'Wisconsin'],
            ['value' => 'US-WY', 'text' => 'Wyoming']
        ],
        'CA' => [ // Canada
            ['value' => 'CA-AB', 'text' => 'Alberta'],
            ['value' => 'CA-BC', 'text' => 'British Columbia'],
            ['value' => 'CA-MB', 'text' => 'Manitoba'],
            ['value' => 'CA-NB', 'text' => 'New Brunswick'],
            ['value' => 'CA-NL', 'text' => 'Newfoundland and Labrador'],
            ['value' => 'CA-NS', 'text' => 'Nova Scotia'],
            ['value' => 'CA-ON', 'text' => 'Ontario'],
            ['value' => 'CA-PE', 'text' => 'Prince Edward Island'],
            ['value' => 'CA-QC', 'text' => 'Quebec'],
            ['value' => 'CA-SK', 'text' => 'Saskatchewan'],
            ['value' => 'CA-NT', 'text' => 'Northwest Territories'],
            ['value' => 'CA-NU', 'text' => 'Nunavut'],
            ['value' => 'CA-YT', 'text' => 'Yukon']
        ],
        'AU' => [ // Australia
            ['value' => 'AU-ACT', 'text' => 'Australian Capital Territory'],
            ['value' => 'AU-NSW', 'text' => 'New South Wales'],
            ['value' => 'AU-NT', 'text' => 'Northern Territory'],
            ['value' => 'AU-QLD', 'text' => 'Queensland'],
            ['value' => 'AU-SA', 'text' => 'South Australia'],
            ['value' => 'AU-TAS', 'text' => 'Tasmania'],
            ['value' => 'AU-VIC', 'text' => 'Victoria'],
            ['value' => 'AU-WA', 'text' => 'Western Australia']
        ],
        'IN' => [ // India
            ['value' => 'IN-AP', 'text' => 'Andhra Pradesh'],
            ['value' => 'IN-AR', 'text' => 'Arunachal Pradesh'],
            ['value' => 'IN-AS', 'text' => 'Assam'],
            ['value' => 'IN-BR', 'text' => 'Bihar'],
            ['value' => 'IN-CT', 'text' => 'Chhattisgarh'],
            ['value' => 'IN-GA', 'text' => 'Goa'],
            ['value' => 'IN-GJ', 'text' => 'Gujarat'],
            ['value' => 'IN-HR', 'text' => 'Haryana'],
            ['value' => 'IN-HP', 'text' => 'Himachal Pradesh'],
            ['value' => 'IN-JH', 'text' => 'Jharkhand'],
            ['value' => 'IN-KA', 'text' => 'Karnataka'],
            ['value' => 'IN-KL', 'text' => 'Kerala'],
            ['value' => 'IN-MP', 'text' => 'Madhya Pradesh'],
            ['value' => 'IN-MH', 'text' => 'Maharashtra'],
            ['value' => 'IN-MN', 'text' => 'Manipur'],
            ['value' => 'IN-ML', 'text' => 'Meghalaya'],
            ['value' => 'IN-MZ', 'text' => 'Mizoram'],
            ['value' => 'IN-NL', 'text' => 'Nagaland'],
            ['value' => 'IN-OR', 'text' => 'Odisha'],
            ['value' => 'IN-PB', 'text' => 'Punjab'],
            ['value' => 'IN-RJ', 'text' => 'Rajasthan'],
            ['value' => 'IN-SK', 'text' => 'Sikkim'],
            ['value' => 'IN-TN', 'text' => 'Tamil Nadu'],
            ['value' => 'IN-TG', 'text' => 'Telangana'],
            ['value' => 'IN-TR', 'text' => 'Tripura'],
            ['value' => 'IN-UP', 'text' => 'Uttar Pradesh'],
            ['value' => 'IN-UT', 'text' => 'Uttarakhand'],
            ['value' => 'IN-WB', 'text' => 'West Bengal'],
            ['value' => 'IN-AN', 'text' => 'Andaman and Nicobar Islands'],
            ['value' => 'IN-CH', 'text' => 'Chandigarh'],
            ['value' => 'IN-DN', 'text' => 'Dadra and Nagar Haveli'],
            ['value' => 'IN-DD', 'text' => 'Daman and Diu'],
            ['value' => 'IN-DL', 'text' => 'Delhi'],
            ['value' => 'IN-JK', 'text' => 'Jammu and Kashmir'],
            ['value' => 'IN-LA', 'text' => 'Ladakh'],
            ['value' => 'IN-LD', 'text' => 'Lakshadweep'],
            ['value' => 'IN-PY', 'text' => 'Puducherry']
        ],
        'TR' => [ // Turkey
            ['value' => 'TR-01', 'text' => 'Adana'],
            ['value' => 'TR-02', 'text' => 'Adiyaman'],
            ['value' => 'TR-03', 'text' => 'Afyonkarahisar'],
            ['value' => 'TR-04', 'text' => 'Agri'],
            ['value' => 'TR-68', 'text' => 'Aksaray'],
            ['value' => 'TR-05', 'text' => 'Amasya'],
            ['value' => 'TR-06', 'text' => 'Ankara'],
            ['value' => 'TR-07', 'text' => 'Antalya'],
            ['value' => 'TR-75', 'text' => 'Ardahan'],
            ['value' => 'TR-08', 'text' => 'Artvin'],
            ['value' => 'TR-09', 'text' => 'Aydin'],
            ['value' => 'TR-10', 'text' => 'Balikesir'],
            ['value' => 'TR-74', 'text' => 'Bartin'],
            ['value' => 'TR-72', 'text' => 'Batman'],
            ['value' => 'TR-69', 'text' => 'Bayburt'],
            ['value' => 'TR-11', 'text' => 'Bilecik'],
            ['value' => 'TR-12', 'text' => 'Bingol'],
            ['value' => 'TR-13', 'text' => 'Bitlis'],
            ['value' => 'TR-14', 'text' => 'Bolu'],
            ['value' => 'TR-15', 'text' => 'Burdur'],
            ['value' => 'TR-16', 'text' => 'Bursa'],
            ['value' => 'TR-17', 'text' => 'Canakkale'],
            ['value' => 'TR-18', 'text' => 'Cankiri'],
            ['value' => 'TR-19', 'text' => 'Corum'],
            ['value' => 'TR-20', 'text' => 'Denizli'],
            ['value' => 'TR-21', 'text' => 'Diyarbakir'],
            ['value' => 'TR-81', 'text' => 'Duzce'],
            ['value' => 'TR-22', 'text' => 'Edirne'],
            ['value' => 'TR-23', 'text' => 'Elazig'],
            ['value' => 'TR-24', 'text' => 'Erzincan'],
            ['value' => 'TR-25', 'text' => 'Erzurum'],
            ['value' => 'TR-26', 'text' => 'Eskisehir'],
            ['value' => 'TR-27', 'text' => 'Gaziantep'],
            ['value' => 'TR-28', 'text' => 'Giresun'],
            ['value' => 'TR-29', 'text' => 'Gumushane'],
            ['value' => 'TR-30', 'text' => 'Hakkari'],
            ['value' => 'TR-31', 'text' => 'Hatay'],
            ['value' => 'TR-76', 'text' => 'Igdir'],
            ['value' => 'TR-32', 'text' => 'Isparta'],
            ['value' => 'TR-34', 'text' => 'Istanbul'],
            ['value' => 'TR-35', 'text' => 'Izmir'],
            ['value' => 'TR-46', 'text' => 'Kahramanmaras'],
            ['value' => 'TR-78', 'text' => 'Karabuk'],
            ['value' => 'TR-70', 'text' => 'Karaman'],
            ['value' => 'TR-36', 'text' => 'Kars'],
            ['value' => 'TR-37', 'text' => 'Kastamonu'],
            ['value' => 'TR-38', 'text' => 'Kayseri'],
            ['value' => 'TR-71', 'text' => 'Kirikkale'],
            ['value' => 'TR-39', 'text' => 'Kirklareli'],
            ['value' => 'TR-40', 'text' => 'Kirsehir'],
            ['value' => 'TR-79', 'text' => 'Kilis'],
            ['value' => 'TR-41', 'text' => 'Kocaeli'],
            ['value' => 'TR-42', 'text' => 'Konya'],
            ['value' => 'TR-43', 'text' => 'Kutahya'],
            ['value' => 'TR-44', 'text' => 'Malatya'],
            ['value' => 'TR-45', 'text' => 'Manisa'],
            ['value' => 'TR-47', 'text' => 'Mardin'],
            ['value' => 'TR-33', 'text' => 'Mersin'],
            ['value' => 'TR-48', 'text' => 'Mugla'],
            ['value' => 'TR-49', 'text' => 'Mus'],
            ['value' => 'TR-50', 'text' => 'Nevsehir'],
            ['value' => 'TR-51', 'text' => 'Nigde'],
            ['value' => 'TR-52', 'text' => 'Ordu'],
            ['value' => 'TR-80', 'text' => 'Osmaniye'],
            ['value' => 'TR-53', 'text' => 'Rize'],
            ['value' => 'TR-54', 'text' => 'Sakarya'],
            ['value' => 'TR-55', 'text' => 'Samsun'],
            ['value' => 'TR-56', 'text' => 'Siirt'],
            ['value' => 'TR-57', 'text' => 'Sinop'],
            ['value' => 'TR-58', 'text' => 'Sivas'],
            ['value' => 'TR-63', 'text' => 'Sanliurfa'],
            ['value' => 'TR-73', 'text' => 'Sirnak'],
            ['value' => 'TR-59', 'text' => 'Tekirdag'],
            ['value' => 'TR-60', 'text' => 'Tokat'],
            ['value' => 'TR-61', 'text' => 'Trabzon'],
            ['value' => 'TR-62', 'text' => 'Tunceli'],
            ['value' => 'TR-64', 'text' => 'Usak'],
            ['value' => 'TR-65', 'text' => 'Van'],
            ['value' => 'TR-77', 'text' => 'Yalova'],
            ['value' => 'TR-66', 'text' => 'Yozgat'],
            ['value' => 'TR-67', 'text' => 'Zonguldak']
        ],
        'JP' => [ // Japan
            ['value' => 'JP-23', 'text' => 'Aichi'],
            ['value' => 'JP-05', 'text' => 'Akita'],
            ['value' => 'JP-02', 'text' => 'Aomori'],
            ['value' => 'JP-38', 'text' => 'Ehime'],
            ['value' => 'JP-21', 'text' => 'Gifu'],
            ['value' => 'JP-10', 'text' => 'Gunma'],
            ['value' => 'JP-34', 'text' => 'Hiroshima'],
            ['value' => 'JP-01', 'text' => 'Hokkaido'],
            ['value' => 'JP-18', 'text' => 'Fukui'],
            ['value' => 'JP-40', 'text' => 'Fukuoka'],
            ['value' => 'JP-07', 'text' => 'Fukushima'],
            ['value' => 'JP-28', 'text' => 'Hyogo'],
            ['value' => 'JP-08', 'text' => 'Ibaraki'],
            ['value' => 'JP-17', 'text' => 'Ishikawa'],
            ['value' => 'JP-03', 'text' => 'Iwate'],
            ['value' => 'JP-37', 'text' => 'Kagawa'],
            ['value' => 'JP-46', 'text' => 'Kagoshima'],
            ['value' => 'JP-14', 'text' => 'Kanagawa'],
            ['value' => 'JP-39', 'text' => 'Kochi'],
            ['value' => 'JP-43', 'text' => 'Kumamoto'],
            ['value' => 'JP-26', 'text' => 'Kyoto'],
            ['value' => 'JP-24', 'text' => 'Mie'],
            ['value' => 'JP-04', 'text' => 'Miyagi'],
            ['value' => 'JP-45', 'text' => 'Miyazaki'],
            ['value' => 'JP-20', 'text' => 'Nagano'],
            ['value' => 'JP-42', 'text' => 'Nagasaki'],
            ['value' => 'JP-29', 'text' => 'Nara'],
            ['value' => 'JP-15', 'text' => 'Niigata'],
            ['value' => 'JP-44', 'text' => 'Oita'],
            ['value' => 'JP-33', 'text' => 'Okayama'],
            ['value' => 'JP-47', 'text' => 'Okinawa'],
            ['value' => 'JP-27', 'text' => 'Osaka'],
            ['value' => 'JP-41', 'text' => 'Saga'],
            ['value' => 'JP-11', 'text' => 'Saitama'],
            ['value' => 'JP-25', 'text' => 'Shiga'],
            ['value' => 'JP-32', 'text' => 'Shimane'],
            ['value' => 'JP-22', 'text' => 'Shizuoka'],
            ['value' => 'JP-09', 'text' => 'Tochigi'],
            ['value' => 'JP-36', 'text' => 'Tokushima'],
            ['value' => 'JP-13', 'text' => 'Tokyo'],
            ['value' => 'JP-31', 'text' => 'Tottori'],
            ['value' => 'JP-16', 'text' => 'Toyama'],
            ['value' => 'JP-30', 'text' => 'Wakayama'],
            ['value' => 'JP-06', 'text' => 'Yamagata'],
            ['value' => 'JP-35', 'text' => 'Yamaguchi'],
            ['value' => 'JP-19', 'text' => 'Yamanashi']
        ],
        'BR' => [ // Brazil
            ['value' => 'BR-AC', 'text' => 'Acre'],
            ['value' => 'BR-AL', 'text' => 'Alagoas'],
            ['value' => 'BR-AP', 'text' => 'Amapá'],
            ['value' => 'BR-AM', 'text' => 'Amazonas'],
            ['value' => 'BR-BA', 'text' => 'Bahia'],
            ['value' => 'BR-CE', 'text' => 'Ceará'],
            ['value' => 'BR-DF', 'text' => 'Distrito Federal'],
            ['value' => 'BR-ES', 'text' => 'Espírito Santo'],
            ['value' => 'BR-GO', 'text' => 'Goiás'],
            ['value' => 'BR-MA', 'text' => 'Maranhão'],
            ['value' => 'BR-MT', 'text' => 'Mato Grosso'],
            ['value' => 'BR-MS', 'text' => 'Mato Grosso do Sul'],
            ['value' => 'BR-MG', 'text' => 'Minas Gerais'],
            ['value' => 'BR-PA', 'text' => 'Pará'],
            ['value' => 'BR-PB', 'text' => 'Paraíba'],
            ['value' => 'BR-PR', 'text' => 'Paraná'],
            ['value' => 'BR-PE', 'text' => 'Pernambuco'],
            ['value' => 'BR-PI', 'text' => 'Piauí'],
            ['value' => 'BR-RJ', 'text' => 'Rio de Janeiro'],
            ['value' => 'BR-RN', 'text' => 'Rio Grande do Norte'],
            ['value' => 'BR-RS', 'text' => 'Rio Grande do Sul'],
            ['value' => 'BR-RO', 'text' => 'Rondônia'],
            ['value' => 'BR-RR', 'text' => 'Roraima'],
            ['value' => 'BR-SC', 'text' => 'Santa Catarina'],
            ['value' => 'BR-SP', 'text' => 'São Paulo'],
            ['value' => 'BR-SE', 'text' => 'Sergipe'],
            ['value' => 'BR-TO', 'text' => 'Tocantins']
        ],
        'MX' => [ // Mexico
            ['value' => 'MX-AGU', 'text' => 'Aguascalientes'],
            ['value' => 'MX-BCN', 'text' => 'Baja California'],
            ['value' => 'MX-BCS', 'text' => 'Baja California Sur'],
            ['value' => 'MX-CAM', 'text' => 'Campeche'],
            ['value' => 'MX-CHP', 'text' => 'Chiapas'],
            ['value' => 'MX-CHH', 'text' => 'Chihuahua'],
            ['value' => 'MX-COA', 'text' => 'Coahuila'],
            ['value' => 'MX-COL', 'text' => 'Colima'],
            ['value' => 'MX-DUR', 'text' => 'Durango'],
            ['value' => 'MX-GUA', 'text' => 'Guanajuato'],
            ['value' => 'MX-GRO', 'text' => 'Guerrero'],
            ['value' => 'MX-HID', 'text' => 'Hidalgo'],
            ['value' => 'MX-JAL', 'text' => 'Jalisco'],
            ['value' => 'MX-MEX', 'text' => 'México'],
            ['value' => 'MX-MIC', 'text' => 'Michoacán'],
            ['value' => 'MX-MOR', 'text' => 'Morelos'],
            ['value' => 'MX-NAY', 'text' => 'Nayarit'],
            ['value' => 'MX-NLE', 'text' => 'Nuevo León'],
            ['value' => 'MX-OAX', 'text' => 'Oaxaca'],
            ['value' => 'MX-PUE', 'text' => 'Puebla'],
            ['value' => 'MX-QUE', 'text' => 'Querétaro'],
            ['value' => 'MX-ROO', 'text' => 'Quintana Roo'],
            ['value' => 'MX-SLP', 'text' => 'San Luis Potosí'],
            ['value' => 'MX-SIN', 'text' => 'Sinaloa'],
            ['value' => 'MX-SON', 'text' => 'Sonora'],
            ['value' => 'MX-TAB', 'text' => 'Tabasco'],
            ['value' => 'MX-TAM', 'text' => 'Tamaulipas'],
            ['value' => 'MX-TLA', 'text' => 'Tlaxcala'],
            ['value' => 'MX-VER', 'text' => 'Veracruz'],
            ['value' => 'MX-YUC', 'text' => 'Yucatán'],
            ['value' => 'MX-ZAC', 'text' => 'Zacatecas'],
            ['value' => 'MX-CMX', 'text' => 'Ciudad de México']
        ],
        'DE' => [ // Germany
            ['value' => 'DE-BW', 'text' => 'Baden-Württemberg'],
            ['value' => 'DE-BY', 'text' => 'Bayern'],
            ['value' => 'DE-BE', 'text' => 'Berlin'],
            ['value' => 'DE-BB', 'text' => 'Brandenburg'],
            ['value' => 'DE-HB', 'text' => 'Bremen'],
            ['value' => 'DE-HH', 'text' => 'Hamburg'],
            ['value' => 'DE-HE', 'text' => 'Hessen'],
            ['value' => 'DE-MV', 'text' => 'Mecklenburg-Vorpommern'],
            ['value' => 'DE-NI', 'text' => 'Niedersachsen'],
            ['value' => 'DE-NW', 'text' => 'Nordrhein-Westfalen'],
            ['value' => 'DE-RP', 'text' => 'Rheinland-Pfalz'],
            ['value' => 'DE-SL', 'text' => 'Saarland'],
            ['value' => 'DE-SN', 'text' => 'Sachsen'],
            ['value' => 'DE-ST', 'text' => 'Sachsen-Anhalt'],
            ['value' => 'DE-SH', 'text' => 'Schleswig-Holstein'],
            ['value' => 'DE-TH', 'text' => 'Thüringen']
        ],
        'CN' => [ // China
            ['value' => 'CN-AH', 'text' => 'Anhui'],
            ['value' => 'CN-BJ', 'text' => 'Beijing'],
            ['value' => 'CN-CQ', 'text' => 'Chongqing'],
            ['value' => 'CN-FJ', 'text' => 'Fujian'],
            ['value' => 'CN-GS', 'text' => 'Gansu'],
            ['value' => 'CN-GD', 'text' => 'Guangdong'],
            ['value' => 'CN-GX', 'text' => 'Guangxi'],
            ['value' => 'CN-GZ', 'text' => 'Guizhou'],
            ['value' => 'CN-HI', 'text' => 'Hainan'],
            ['value' => 'CN-HE', 'text' => 'Hebei'],
            ['value' => 'CN-HL', 'text' => 'Heilongjiang'],
            ['value' => 'CN-HA', 'text' => 'Henan'],
            ['value' => 'CN-HB', 'text' => 'Hubei'],
            ['value' => 'CN-HN', 'text' => 'Hunan'],
            ['value' => 'CN-JS', 'text' => 'Jiangsu'],
            ['value' => 'CN-JX', 'text' => 'Jiangxi'],
            ['value' => 'CN-JL', 'text' => 'Jilin'],
            ['value' => 'CN-LN', 'text' => 'Liaoning'],
            ['value' => 'CN-NM', 'text' => 'Inner Mongolia'],
            ['value' => 'CN-NX', 'text' => 'Ningxia'],
            ['value' => 'CN-QH', 'text' => 'Qinghai'],
            ['value' => 'CN-SN', 'text' => 'Shaanxi'],
            ['value' => 'CN-SD', 'text' => 'Shandong'],
            ['value' => 'CN-SH', 'text' => 'Shanghai'],
            ['value' => 'CN-SX', 'text' => 'Shanxi'],
            ['value' => 'CN-SC', 'text' => 'Sichuan'],
            ['value' => 'CN-TJ', 'text' => 'Tianjin'],
            ['value' => 'CN-XZ', 'text' => 'Tibet'],
            ['value' => 'CN-XJ', 'text' => 'Xinjiang'],
            ['value' => 'CN-YN', 'text' => 'Yunnan'],
            ['value' => 'CN-ZJ', 'text' => 'Zhejiang']
        ]
    ];

    return $fallbacks[$countryCode] ?? [];
}
}

// Get country code from request
$countryCode = $_GET['country'] ?? $_POST['country'] ?? '';

if (empty($countryCode)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Country code is required'
    ]);
    exit;
}

try {
    // Set a timeout to prevent hanging
    set_time_limit(10);

    // For now, use fallback data directly to avoid geographer hanging issues
    $states = [];
    $fallbackStates = getFallbackStates(strtoupper($countryCode));

    if (!empty($fallbackStates)) {
        // Convert fallback format to expected format
        foreach ($fallbackStates as $state) {
            $states[] = [
                'iso_code' => $state['value'],
                'english_name' => $state['text'],
                'local_name' => $state['text']
            ];
        }
    } else {
        // Try geographer as fallback
        $states = getStatesMapping(strtoupper($countryCode));
    }
    
    if (empty($states)) {
        // If no states found, return empty array (some countries might not have states)
        echo json_encode([
            'success' => true,
            'country' => strtoupper($countryCode),
            'states' => []
        ]);
        exit;
    }
    
    // Format states for dropdown
    $formattedStates = [];
    foreach ($states as $state) {
        $formattedStates[] = [
            'value' => $state['iso_code'] ?? $state['code'], // Use ISO code as value
            'text' => $state['english_name'], // Display English name
            'local_name' => $state['local_name'] ?? $state['english_name'] // Keep local name for reference
        ];
    }
    
    // Sort states alphabetically by English name
    usort($formattedStates, function($a, $b) {
        return strcmp($a['text'], $b['text']);
    });
    
    echo json_encode([
        'success' => true,
        'country' => strtoupper($countryCode),
        'states' => $formattedStates
    ]);
    
} catch (Exception $e) {
    error_log("Get states API error: " . $e->getMessage());

    // Try fallback for common countries
    $fallbackStates = getFallbackStates(strtoupper($countryCode));
    if (!empty($fallbackStates)) {
        echo json_encode([
            'success' => true,
            'country' => strtoupper($countryCode),
            'states' => $fallbackStates
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to get states for country: ' . $countryCode,
            'message' => $e->getMessage()
        ]);
    }
}
?>