<?php
/**
 * n8n Webhook Endpoint for HelloIT Integration
 * This file handles incoming webhooks from n8n workflows
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include necessary files
require_once '../functions/server.php';
require_once '../config/environment-config.php';

// Log function for debugging
function logWebhook($message, $data = null) {
    $log_file = '../logs/n8n_webhook.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message";
    
    if ($data !== null) {
        $log_entry .= "\nData: " . json_encode($data, JSON_PRETTY_PRINT);
    }
    
    $log_entry .= "\n" . str_repeat('-', 50) . "\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Get the request method and data
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Get headers
$headers = getallheaders();

// Log the incoming request
logWebhook("Incoming n8n webhook", [
    'method' => $method,
    'headers' => $headers,
    'data' => $data,
    'query_params' => $_GET
]);

try {
    // Validate the request
    if (!$data && $method === 'POST') {
        throw new Exception('No data received in POST request');
    }

    // Get the action from URL parameter or data
    $action = $_GET['action'] ?? $data['action'] ?? 'unknown';

    // Response array
    $response = [
        'success' => false,
        'message' => '',
        'data' => null,
        'timestamp' => date('c')
    ];

    // Handle different actions
    switch ($action) {
        case 'test':
            // Test endpoint
            $response['success'] = true;
            $response['message'] = 'n8n webhook endpoint is working!';
            $response['data'] = [
                'helloit_version' => '1.0',
                'php_version' => PHP_VERSION,
                'timestamp' => time()
            ];
            break;

        case 'ticket_created':
            // Handle new ticket creation from n8n
            if (!isset($data['ticket_id'])) {
                throw new Exception('ticket_id is required for ticket_created action');
            }
            
            $ticket_id = $data['ticket_id'];
            $customer_email = $data['customer_email'] ?? '';
            $subject = $data['subject'] ?? '';
            
            // Process the ticket creation
            // Add your ticket processing logic here
            
            $response['success'] = true;
            $response['message'] = 'Ticket created successfully';
            $response['data'] = [
                'ticket_id' => $ticket_id,
                'processed_at' => date('c')
            ];
            break;

        case 'ticket_updated':
            // Handle ticket updates from n8n
            if (!isset($data['ticket_id'])) {
                throw new Exception('ticket_id is required for ticket_updated action');
            }
            
            $ticket_id = $data['ticket_id'];
            $status = $data['status'] ?? '';
            $notes = $data['notes'] ?? '';
            
            // Process the ticket update
            // Add your ticket update logic here
            
            $response['success'] = true;
            $response['message'] = 'Ticket updated successfully';
            $response['data'] = [
                'ticket_id' => $ticket_id,
                'new_status' => $status,
                'updated_at' => date('c')
            ];
            break;

        case 'send_notification':
            // Handle notification sending from n8n
            $recipient = $data['recipient'] ?? '';
            $message = $data['message'] ?? '';
            $type = $data['type'] ?? 'email';
            
            if (!$recipient || !$message) {
                throw new Exception('recipient and message are required for send_notification action');
            }
            
            // Process the notification
            // Add your notification logic here
            
            $response['success'] = true;
            $response['message'] = 'Notification sent successfully';
            $response['data'] = [
                'recipient' => $recipient,
                'type' => $type,
                'sent_at' => date('c')
            ];
            break;

        case 'payment_processed':
            // Handle payment processing from n8n
            $payment_id = $data['payment_id'] ?? '';
            $amount = $data['amount'] ?? 0;
            $customer_id = $data['customer_id'] ?? '';
            
            if (!$payment_id || !$amount) {
                throw new Exception('payment_id and amount are required for payment_processed action');
            }
            
            // Process the payment
            // Add your payment processing logic here
            
            $response['success'] = true;
            $response['message'] = 'Payment processed successfully';
            $response['data'] = [
                'payment_id' => $payment_id,
                'amount' => $amount,
                'processed_at' => date('c')
            ];
            break;

        case 'get_tickets':
            // Get tickets data for n8n workflows
            $limit = $_GET['limit'] ?? 10;
            $status = $_GET['status'] ?? '';
            
            // Fetch tickets from database
            // Add your ticket fetching logic here
            
            $response['success'] = true;
            $response['message'] = 'Tickets retrieved successfully';
            $response['data'] = [
                'tickets' => [], // Your ticket data here
                'count' => 0,
                'retrieved_at' => date('c')
            ];
            break;

        case 'get_customers':
            // Get customer data for n8n workflows
            $limit = $_GET['limit'] ?? 10;
            
            // Fetch customers from database
            // Add your customer fetching logic here
            
            $response['success'] = true;
            $response['message'] = 'Customers retrieved successfully';
            $response['data'] = [
                'customers' => [], // Your customer data here
                'count' => 0,
                'retrieved_at' => date('c')
            ];
            break;

        default:
            throw new Exception("Unknown action: $action");
    }

    // Log successful response
    logWebhook("Webhook processed successfully", $response);

    // Send response
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    // Log error
    logWebhook("Webhook error: " . $e->getMessage(), [
        'action' => $action,
        'input_data' => $data,
        'error_trace' => $e->getTraceAsString()
    ]);

    // Send error response
    $error_response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => $e->getCode(),
        'timestamp' => date('c')
    ];

    http_response_code(400);
    echo json_encode($error_response, JSON_PRETTY_PRINT);
}
?>