<?php
/**
 * Edit Profile with Appika API Integration
 * Updates customer data in Appika API and local database
 */

// Check if session is already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files only if not already included
if (!class_exists('DBController')) {
    include('server.php');
}
if (!function_exists('getCompleteCustomerData')) {
    include('customer-data-service.php');
}
require_once '../vendor/autoload.php';
require_once '../config/api-config.php';

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header("Location: ../support-ticket/sign-in");
    exit();
}

$username = $_SESSION['username'];
$errors = array();

// Get current user data
$user = getCompleteCustomerData($username);
if (!$user) {
    header("Location: ../support-ticket/profile?error=1");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $first_name = trim($_POST['first_name'] ?? '');
    $tell = trim($_POST['tell'] ?? '');
    $company_name = trim($_POST['company_name'] ?? '');
    $tax_id = trim($_POST['tax_id'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $address2 = trim($_POST['address2'] ?? '');
    $city = trim($_POST['city'] ?? '');
    $state = trim($_POST['state'] ?? '');
    $country = trim($_POST['country'] ?? '');
    $postal_code = trim($_POST['postal_code'] ?? '');
    $email = trim($_POST['email'] ?? '');

    // Validation
    if (empty($first_name)) {
        $errors[] = "Full name is required";
    }
    if (empty($address)) {
        $errors[] = "Address is required";
    }
    // City is optional - no validation required
    if (empty($country)) {
        $errors[] = "Country is required";
    }

    // Duplicate email check
    if (!empty($email)) {
        $check_stmt = $conn->prepare("SELECT id FROM user WHERE email = ? AND status IN ('a', 'i') AND username != ?");
        $check_stmt->bind_param("ss", $email, $username);
        $check_stmt->execute();
        $check_stmt->store_result();
        if ($check_stmt->num_rows > 0) {
            $errors[] = "This email is already in use by another active or suspended account.";
        }
        $check_stmt->close();
    }

    if (empty($errors)) {
        try {
            // Update Appika API if user has Appika data
            if ($user['has_appika_data'] && !empty($user['appika_customer_id'])) {
                $updateResult = updateCustomerInAppika($user['appika_customer_id'], [
                    'name' => $first_name,
                    'tel_work' => $tell,
                    'address' => $address,
                    'address2' => $address2,
                    'city' => $city,
                    'state' => $state,
                    'country' => $country,
                    'postal_code' => $postal_code,
                    'email' => $email
                ]);

                if (!$updateResult['success']) {
                    $errors[] = "Failed to update profile in Appika API: " . $updateResult['message'];
                }
            } else {
                // Log the issue for debugging
                error_log("Profile update failed - User data: has_appika_data=" . ($user['has_appika_data'] ? 'true' : 'false') . ", appika_customer_id=" . ($user['appika_customer_id'] ?? 'null'));
                $errors[] = "Unable to update profile: Customer not linked to Appika API";
            }

            // Always update first_name, email, company_name, and tax_id in local database (regardless of Appika API result)
            $stmt = $conn->prepare("UPDATE user SET first_name = ?, email = ?, company_name = ?, tax_id = ? WHERE username = ?");
            $stmt->bind_param("sssss", $first_name, $email, $company_name, $tax_id, $username);

            $local_update_success = false;
            if ($stmt->execute()) {
                $local_update_success = true;
                error_log("Local database updated for user: $username - first_name, email, company_name, and tax_id saved");
            } else {
                $errors[] = "Failed to update profile information in local database";
                error_log("Failed to update local database for user: $username - " . $stmt->error);
            }
            $stmt->close();

            // Update timezone in database to match selected country
            $country_timezone_map = [
                'SG' => 'Asia/Singapore', 'TH' => 'Asia/Bangkok', 'MY' => 'Asia/Kuala_Lumpur', 'ID' => 'Asia/Jakarta',
                'PH' => 'Asia/Manila', 'VN' => 'Asia/Ho_Chi_Minh', 'KH' => 'Asia/Phnom_Penh', 'LA' => 'Asia/Vientiane',
                'MM' => 'Asia/Yangon', 'BN' => 'Asia/Brunei', 'CN' => 'Asia/Shanghai', 'JP' => 'Asia/Tokyo',
                'KR' => 'Asia/Seoul', 'IN' => 'Asia/Kolkata', 'TW' => 'Asia/Taipei', 'HK' => 'Asia/Hong_Kong',
                'AU' => 'Australia/Sydney', 'NZ' => 'Pacific/Auckland', 'US' => 'America/New_York', 'GB' => 'Europe/London',
                'DE' => 'Europe/Berlin', 'FR' => 'Europe/Paris', 'RU' => 'Europe/Moscow', 'BR' => 'America/Sao_Paulo',
                'ZA' => 'Africa/Johannesburg', 'SA' => 'Asia/Riyadh', 'AE' => 'Asia/Dubai', 'CA' => 'America/Toronto',
                'ES' => 'Europe/Madrid', 'IT' => 'Europe/Rome', 'TR' => 'Europe/Istanbul', 'EG' => 'Africa/Cairo',
                'AR' => 'America/Argentina/Buenos_Aires', 'PK' => 'Asia/Karachi', 'BD' => 'Asia/Dhaka', 'LK' => 'Asia/Colombo',
                'NP' => 'Asia/Kathmandu', 'IR' => 'Asia/Tehran', 'IQ' => 'Asia/Baghdad', 'IL' => 'Asia/Jerusalem',
                'UA' => 'Europe/Kyiv', 'PL' => 'Europe/Warsaw', 'SE' => 'Europe/Stockholm', 'NO' => 'Europe/Oslo',
                'FI' => 'Europe/Helsinki', 'DK' => 'Europe/Copenhagen', 'NL' => 'Europe/Amsterdam', 'BE' => 'Europe/Brussels',
                'CH' => 'Europe/Zurich', 'AT' => 'Europe/Vienna', 'GR' => 'Europe/Athens', 'HU' => 'Europe/Budapest',
                'CZ' => 'Europe/Prague', 'SK' => 'Europe/Bratislava', 'RO' => 'Europe/Bucharest', 'BG' => 'Europe/Sofia',
                'HR' => 'Europe/Zagreb', 'SI' => 'Europe/Ljubljana', 'RS' => 'Europe/Belgrade', 'BA' => 'Europe/Sarajevo',
                'ME' => 'Europe/Podgorica', 'MK' => 'Europe/Skopje', 'AL' => 'Europe/Tirane', 'LT' => 'Europe/Vilnius',
                'LV' => 'Europe/Riga', 'EE' => 'Europe/Tallinn', 'BY' => 'Europe/Minsk', 'MD' => 'Europe/Chisinau',
                'GE' => 'Asia/Tbilisi', 'AM' => 'Asia/Yerevan', 'AZ' => 'Asia/Baku', 'KZ' => 'Asia/Almaty',
                'UZ' => 'Asia/Tashkent', 'KG' => 'Asia/Bishkek', 'TJ' => 'Asia/Dushanbe', 'TM' => 'Asia/Ashgabat',
                'MN' => 'Asia/Ulaanbaatar', 'AF' => 'Asia/Kabul', 'TH' => 'Asia/Bangkok', 'SG' => 'Asia/Singapore',
                // Add more as needed
            ];
            if (!empty($country) && isset($country_timezone_map[$country])) {
                $timezone = $country_timezone_map[$country];
                $stmt = $conn->prepare("UPDATE user SET timezone = ? WHERE username = ?");
                $stmt->bind_param("ss", $timezone, $username);
                $stmt->execute();
                $stmt->close();
            }

            // Redirect with success if either Appika update succeeded OR local update succeeded
            // (since company/tax data is more important and always needs to be saved)
            if (empty($errors) || $local_update_success) {
                // Log the overall result
                if (empty($errors)) {
                    error_log("Profile fully updated for user: $username - Both Appika API and local database updated");
                } else {
                    error_log("Profile partially updated for user: $username - Local database updated, but Appika API had issues");
                }

                header("Location: ../support-ticket/profile?success=1");
                exit();
            }
        } catch (Exception $e) {
            error_log("Error updating profile for user $username: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $errors[] = "An error occurred while updating your profile. Please try again.";
        }
    }

    // If there are errors, redirect back with error
    if (!empty($errors)) {
        $_SESSION['profile_errors'] = $errors;
        header("Location: ../support-ticket/profile?error=2");
        exit();
    }
}

/**
 * Update customer data in Appika API
 */
function updateCustomerInAppika($customerId, $customerData) {
    try {
        // Get API configuration
        $apiConfig = getCustomerApiConfig();
        $apiEndpoint = $apiConfig['endpoint'];
        $apiPath = $apiConfig['path'];
        $apiKey = $apiConfig['key'];

        // Create Guzzle client
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);

        // First, get the existing customer data to preserve required fields
        $getResponse = $client->request('GET', $apiPath . '/' . $customerId, [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);

        $getStatusCode = $getResponse->getStatusCode();
        $getBody = $getResponse->getBody()->getContents();

        if ($getStatusCode >= 200 && $getStatusCode < 300) {
            $existingData = json_decode($getBody, true);

            // Prepare customer update data with all required fields from existing data
            $updateData = [
                'no' => $existingData['no'] ?? '',
                'name' => $customerData['name'],
                'entity_type' => $existingData['entity_type'] ?? '1',
                'grp_id' => $existingData['grp_id'] ?? 10,
                'ofc_id' => $existingData['ofc_id'] ?? 511,
                'assign2' => $existingData['assign2'] ?? 1,
                'creator' => $existingData['creator'] ?? 1,
                'start_date' => $existingData['start_date'] ?? date('Y-m-d'),
                'status' => $existingData['status'] ?? 'a',
                'email' => $customerData['email'] ?? ($existingData['email'] ?? '')
            ];

            // Update customer basic info
            $response = $client->request('PUT', $apiPath . '/' . $customerId, [
                'headers' => [
                    'X-api-key' => "{$apiKey}",
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                'json' => $updateData
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
        } else {
            return [
                'success' => false,
                'message' => "Failed to get existing customer data: HTTP $getStatusCode - $getBody"
            ];
        }

        if ($statusCode >= 200 && $statusCode < 300) {
            // Update location data if address information is provided
            if (!empty($customerData['address']) || !empty($customerData['city'])) {
                $locationResult = updateCustomerLocationInAppika($customerId, $customerData);
                if (!$locationResult['success']) {
                    return $locationResult;
                }
            }

            return [
                'success' => true,
                'message' => 'Customer updated successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => "Failed to update customer: HTTP $statusCode - $body"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error updating customer: ' . $e->getMessage()
        ];
    }
}

/**
 * Update customer location data in Appika API
 */
function updateCustomerLocationInAppika($customerId, $locationData) {
    try {
        // Get API configuration
        $apiConfig = getCustomerApiConfig();
        $apiEndpoint = $apiConfig['endpoint'];
        $apiPath = $apiConfig['path'];
        $apiKey = $apiConfig['key'];

        // Create Guzzle client
        $client = new \GuzzleHttp\Client([
            'base_uri' => $apiEndpoint,
            'timeout' => 30,
            'http_errors' => false,
        ]);

        // First, get existing locations
        $locationsResponse = $client->request('GET', $apiPath . '/' . $customerId . '/locations', [
            'headers' => [
                'X-api-key' => "{$apiKey}",
                'Accept' => 'application/json',
            ]
        ]);

        $locationsStatusCode = $locationsResponse->getStatusCode();
        $locationsBody = $locationsResponse->getBody()->getContents();

        if ($locationsStatusCode >= 200 && $locationsStatusCode < 300) {
            $locationsData = json_decode($locationsBody, true);
            $locations = $locationsData['items'] ?? [];

            // Find primary location or create new one
            $primaryLocation = null;
            foreach ($locations as $location) {
                if ($location['is_primary_loc'] === 'y') {
                    $primaryLocation = $location;
                    break;
                }
            }

            // Prepare location update data
            $updateLocationData = [
                'add1' => $locationData['address'] ?? '',
                'add2' => $locationData['address2'] ?? '',
                'city' => $locationData['city'] ?? '',
                'state_code' => $locationData['state'] ?? '',
                'state_name' => $locationData['state'] ?? '', // <-- Add this line for display compatibility
                'ccode' => $locationData['country'] ?? '',
                'zip' => $locationData['postal_code'] ?? '',
                'tel_work' => $locationData['tel_work'] ?? '',
                'is_primary_loc' => 'y',
                'status' => 'a',
                'email' => $locationData['email'] ?? ''
            ];

            // Add required fields if updating existing location
            if ($primaryLocation) {
                $updateLocationData['loc_code'] = $primaryLocation['loc_code'];
                $updateLocationData['loc_name'] = $primaryLocation['loc_name'];
            }

            if ($primaryLocation) {
                // Update existing primary location
                $locationId = $primaryLocation['id'];
                $response = $client->request('PUT', $apiPath . '/' . $customerId . '/locations/' . $locationId, [
                    'headers' => [
                        'X-api-key' => "{$apiKey}",
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $updateLocationData
                ]);
            } else {
                // Create new primary location
                $updateLocationData['loc_code'] = 'LOC-' . uniqid();
                $updateLocationData['loc_name'] = 'Customer Location';
                $updateLocationData['parent_id'] = 0;

                $response = $client->request('POST', $apiPath . '/' . $customerId . '/locations', [
                    'headers' => [
                        'X-api-key' => "{$apiKey}",
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $updateLocationData
                ]);
            }

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode >= 200 && $statusCode < 300) {
                return [
                    'success' => true,
                    'message' => 'Location updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Failed to update location: HTTP $statusCode - $body"
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => "Failed to get locations: HTTP $locationsStatusCode - $locationsBody"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error updating location: ' . $e->getMessage()
        ];
    }
}


?>